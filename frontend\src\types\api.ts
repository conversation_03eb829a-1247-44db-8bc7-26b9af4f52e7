// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: PaginationInfo;
}

// Product Types
export const ProductUnit = {
  PIECE: "PIECE",
  KG: "KG",
  GRAM: "GRAM",
  LITER: "LITER",
  ML: "ML",
  METER: "METER",
  CM: "CM",
  PACKAGE: "PACKAGE",
  BOX: "BOX",
} as const;

export type ProductUnit = (typeof ProductUnit)[keyof typeof ProductUnit];

export interface Product {
  id: string;
  categoryId: string;
  code: string;
  barcode?: string;
  name: string;
  description?: string;
  shortDescription?: string;
  image?: string;
  images: string[];
  basePrice: number;
  taxId: string;
  costPrice?: number;
  profitMargin?: number;
  trackStock: boolean;
  unit: ProductUnit;
  criticalStock?: number;
  available: boolean;
  sellable: boolean;
  preparationTime?: number;
  calories?: number;
  allergens: string[];
  hasVariants: boolean;
  hasModifiers: boolean;
  showInMenu: boolean;
  featured: boolean;
  displayOrder: number;
  active: boolean;
  companyId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  version: number;
  // Relations
  category?: Category;
  tax?: Tax;
  variants?: ProductVariant[];
  _count?: {
    variants: number;
    modifierGroups: number;
  };
}

export interface ProductVariant {
  id: string;
  name: string;
  code: string;
  price: number;
  active: boolean;
}

export interface CreateProductRequest {
  categoryId: string;
  code: string;
  barcode?: string;
  name: string;
  description?: string;
  shortDescription?: string;
  image?: string;
  images?: string[];
  basePrice: number;
  taxId: string;
  costPrice?: number;
  profitMargin?: number;
  trackStock?: boolean;
  unit?: ProductUnit;
  criticalStock?: number;
  available?: boolean;
  sellable?: boolean;
  preparationTime?: number;
  calories?: number;
  allergens?: string[];
  hasVariants?: boolean;
  hasModifiers?: boolean;
  showInMenu?: boolean;
  featured?: boolean;
  displayOrder?: number;
  active?: boolean;
}

export interface UpdateProductRequest extends Partial<CreateProductRequest> {}

export interface ProductQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  categoryId?: string;
  available?: boolean;
  sellable?: boolean;
  active?: boolean;
  trackStock?: boolean;
  sortBy?: "name" | "basePrice" | "createdAt" | "displayOrder";
  sortOrder?: "asc" | "desc";
}

// Category Types
export interface Category {
  id: string;
  parentId?: string;
  name: string;
  description?: string;
  image?: string;
  color?: string;
  icon?: string;
  showInKitchen: boolean;
  preparationTime?: number;
  displayOrder: number;
  active: boolean;
  showInMenu: boolean;
  companyId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  version: number;
  // Relations
  parent?: Category;
  children?: Category[];
  _count?: {
    products: number;
  };
}

export interface CreateCategoryRequest {
  parentId?: string;
  name: string;
  description?: string;
  image?: string;
  color?: string;
  icon?: string;
  showInKitchen?: boolean;
  preparationTime?: number;
  displayOrder?: number;
  active?: boolean;
  showInMenu?: boolean;
}

export interface UpdateCategoryRequest extends Partial<CreateCategoryRequest> {}

export interface CategoryQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  parentId?: string | null;
  active?: boolean;
  showInMenu?: boolean;
  includeHierarchy?: boolean;
  sortBy?: "name" | "displayOrder" | "createdAt";
  sortOrder?: "asc" | "desc";
}

// Tax Types
export const TaxType = {
  VAT: "VAT",
  OTV: "OTV",
  OIV: "OIV",
  DAMGA: "DAMGA",
} as const;

export type TaxType = (typeof TaxType)[keyof typeof TaxType];

export interface Tax {
  id: string;
  code: string;
  name: string;
  rate: number;
  type: TaxType;
  description?: string;
  isDefault: boolean;
  isIncluded: boolean;
  active: boolean;
  companyId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  version: number;
  // Relations
  products?: Product[];
  _count?: {
    products: number;
  };
}

export interface CreateTaxRequest {
  code: string;
  name: string;
  rate: number;
  type: TaxType;
  description?: string;
  isDefault?: boolean;
  isIncluded?: boolean;
  active?: boolean;
}

export interface UpdateTaxRequest extends Partial<CreateTaxRequest> {}

export interface TaxQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: TaxType;
  active?: boolean;
  isDefault?: boolean;
  sortBy?: "name" | "code" | "rate" | "createdAt";
  sortOrder?: "asc" | "desc";
}

// ProductVariant Types
export interface ProductVariant {
  id: string;
  productId: string;
  name: string;
  code: string;
  sku?: string;
  barcode?: string;
  price: number;
  costPrice?: number;
  displayOrder: number;
  active: boolean;
  version: number;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  // Relations
  product?: {
    id: string;
    name: string;
    code: string;
    basePrice: number;
    category?: {
      id: string;
      name: string;
    };
  };
}

export interface CreateProductVariantRequest {
  productId: string;
  name: string;
  code: string;
  sku?: string;
  barcode?: string;
  price: number;
  costPrice?: number;
  displayOrder?: number;
  active?: boolean;
}

export interface UpdateProductVariantRequest
  extends Partial<Omit<CreateProductVariantRequest, "productId">> {}

export interface ProductVariantQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  productId?: string;
  active?: boolean;
  sortBy?: "name" | "price" | "displayOrder" | "createdAt";
  sortOrder?: "asc" | "desc";
}

// ModifierGroup Types
export interface ModifierGroup {
  id: string;
  name: string;
  description?: string;
  minSelection: number;
  maxSelection: number;
  required: boolean;
  freeSelection: number;
  displayOrder: number;
  active: boolean;
  version: number;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  // Relations
  modifiers?: {
    id: string;
    name: string;
    price: number;
    active: boolean;
    displayOrder: number;
  }[];
  products?: {
    product: {
      id: string;
      name: string;
      code: string;
    };
  }[];
  _count?: {
    modifiers: number;
    products: number;
  };
}

export interface CreateModifierGroupRequest {
  name: string;
  description?: string;
  minSelection?: number;
  maxSelection?: number;
  required?: boolean;
  freeSelection?: number;
  displayOrder?: number;
  active?: boolean;
}

export interface UpdateModifierGroupRequest
  extends Partial<CreateModifierGroupRequest> {}

export interface ModifierGroupQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  active?: boolean;
  required?: boolean;
  sortBy?: "name" | "displayOrder" | "createdAt";
  sortOrder?: "asc" | "desc";
}

// Modifier Types
export interface Modifier {
  id: string;
  groupId: string;
  name: string;
  price: number;
  maxQuantity: number;
  inventoryItemId?: string;
  displayOrder: number;
  active: boolean;
  version: number;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  // Relations
  group?: {
    id: string;
    name: string;
    description?: string;
    minSelection: number;
    maxSelection: number;
    required: boolean;
    freeSelection: number;
    active: boolean;
  };
  inventoryItem?: {
    id: string;
    name: string;
    code: string;
    currentStock: number;
    unit: ProductUnit;
    criticalLevel?: number;
    optimalLevel?: number;
  };
}

export interface CreateModifierRequest {
  groupId: string;
  name: string;
  price?: number;
  maxQuantity?: number;
  inventoryItemId?: string;
  displayOrder?: number;
  active?: boolean;
}

export interface UpdateModifierRequest
  extends Partial<Omit<CreateModifierRequest, "groupId">> {}

export interface ModifierQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  groupId?: string;
  active?: boolean;
  hasInventoryItem?: boolean;
  sortBy?: "name" | "price" | "displayOrder" | "createdAt";
  sortOrder?: "asc" | "desc";
}
