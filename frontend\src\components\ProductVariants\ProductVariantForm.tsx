import { zodResolver } from "@hookform/resolvers/zod";
import { X } from "lucide-react";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";
import type {
  CreateProductVariantRequest,
  ProductVariant,
  UpdateProductVariantRequest,
} from "../../types/api";
import { Button } from "../ui/Button";
import { Input } from "../ui/Input";
import { Switch } from "../ui/Switch";

// Validation schema
const productVariantSchema = z.object({
  productId: z.string().min(1, "Ürün seçimi zorunludur"),
  name: z
    .string()
    .min(1, "Varyant adı zorunludur")
    .max(100, "Varyant adı en fazla 100 karakter olabilir"),
  code: z
    .string()
    .min(1, "Varyant kodu zorunludur")
    .max(20, "Varyant kodu en fazla 20 karakter olabilir"),
  sku: z.string().max(50, "SKU en fazla 50 karakter olabilir").default(""),
  barcode: z
    .string()
    .max(50, "Barkod en fazla 50 karakter olabilir")
    .default(""),
  price: z.number().positive("Fiyat pozitif bir sayı olmalıdır"),
  costPrice: z.number().min(0, "Maliyet fiyatı negatif olamaz").default(0),
  displayOrder: z
    .number()
    .int()
    .min(0, "Görüntüleme sırası negatif olamaz")
    .default(0),
  active: z.boolean().default(true),
});

type ProductVariantFormData = z.infer<typeof productVariantSchema>;

interface ProductVariantFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (
    data: CreateProductVariantRequest | UpdateProductVariantRequest
  ) => void;
  variant?: ProductVariant;
  productId: string;
  isLoading?: boolean;
}

export const ProductVariantForm: React.FC<ProductVariantFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  variant,
  productId,
  isLoading = false,
}) => {
  const { t } = useTranslation();
  const isEditing = !!variant;

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm<ProductVariantFormData>({
    resolver: zodResolver(productVariantSchema),
    defaultValues: {
      productId,
      name: "",
      code: "",
      sku: "",
      barcode: "",
      price: 0,
      costPrice: 0,
      displayOrder: 0,
      active: true,
    },
  });

  // Form verilerini variant'tan doldur
  useEffect(() => {
    if (variant) {
      setValue("productId", variant.productId);
      setValue("name", variant.name);
      setValue("code", variant.code);
      setValue("sku", variant.sku || "");
      setValue("barcode", variant.barcode || "");
      setValue("price", variant.price);
      setValue("costPrice", variant.costPrice || 0);
      setValue("displayOrder", variant.displayOrder);
      setValue("active", variant.active);
    } else {
      reset({
        productId,
        name: "",
        code: "",
        sku: "",
        barcode: "",
        price: 0,
        costPrice: 0,
        displayOrder: 0,
        active: true,
      });
    }
  }, [variant, productId, setValue, reset]);

  const handleFormSubmit = (data: ProductVariantFormData) => {
    if (isEditing) {
      const { productId: _, ...updateData } = data;
      onSubmit(updateData);
    } else {
      onSubmit(data);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? t("variants.editVariant") : t("variants.addVariant")}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form
          onSubmit={handleSubmit(handleFormSubmit)}
          className="p-6 space-y-6"
        >
          {/* Varyant Adı */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t("variants.variantName")} *
            </label>
            <Input
              {...register("name")}
              placeholder={t("variants.variantName")}
              error={errors.name?.message}
              className="bg-[#292C2D] dark:bg-[#292C2D]"
            />
          </div>

          {/* Varyant Kodu */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t("variants.variantCode")} *
            </label>
            <Input
              {...register("code")}
              placeholder={t("variants.variantCode")}
              error={errors.code?.message}
              className="bg-[#292C2D] dark:bg-[#292C2D]"
            />
          </div>

          {/* SKU ve Barkod */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t("variants.sku")}
              </label>
              <Input
                {...register("sku")}
                placeholder={t("variants.sku")}
                error={errors.sku?.message}
                className="bg-[#292C2D] dark:bg-[#292C2D]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t("variants.barcode")}
              </label>
              <Input
                {...register("barcode")}
                placeholder={t("variants.barcode")}
                error={errors.barcode?.message}
                className="bg-[#292C2D] dark:bg-[#292C2D]"
              />
            </div>
          </div>

          {/* Fiyat ve Maliyet */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t("variants.price")} * (₺)
              </label>
              <Input
                {...register("price", { valueAsNumber: true })}
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                error={errors.price?.message}
                className="bg-[#292C2D] dark:bg-[#292C2D]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t("variants.costPrice")} (₺)
              </label>
              <Input
                {...register("costPrice", { valueAsNumber: true })}
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                error={errors.costPrice?.message}
                className="bg-[#292C2D] dark:bg-[#292C2D]"
              />
            </div>
          </div>

          {/* Görüntüleme Sırası */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t("variants.displayOrder")}
            </label>
            <Input
              {...register("displayOrder", { valueAsNumber: true })}
              type="number"
              min="0"
              placeholder="0"
              error={errors.displayOrder?.message}
              className="bg-[#292C2D] dark:bg-[#292C2D]"
            />
          </div>

          {/* Aktif Durumu */}
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("variants.active")}
            </label>
            <Switch
              checked={watch("active")}
              onCheckedChange={checked => setValue("active", checked)}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              {t("common.cancel")}
            </Button>
            <Button
              type="submit"
              disabled={!isValid || isLoading}
              loading={isLoading}
            >
              {isEditing ? t("common.update") : t("common.add")}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
