import {
  CheckCircleIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  PlusIcon,
  TrashIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";
import React, { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useCategories, useDeleteCategory } from "../../hooks/useCategories";
import { formatDate } from "../../lib/utils";
import type { Category, CategoryQueryParams } from "../../types/api";
import { Button } from "../ui/Button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/Card";
import { Input } from "../ui/Input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/Table";

interface CategoryListProps {
  onAddCategory: () => void;
  onEditCategory: (category: Category) => void;
}

export const CategoryList: React.FC<CategoryListProps> = ({
  onAddCategory,
  onEditCategory,
}) => {
  const { t } = useTranslation();

  // State
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState<"name" | "displayOrder" | "createdAt">(
    "displayOrder"
  );
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  // Query parameters
  const queryParams: CategoryQueryParams = useMemo(
    () => ({
      page: currentPage,
      limit: 20,
      search: searchTerm || undefined,
      sortBy,
      sortOrder,
    }),
    [currentPage, searchTerm, sortBy, sortOrder]
  );

  // Queries
  const {
    data: categoriesResponse,
    isLoading,
    error,
  } = useCategories(queryParams);
  const deleteCategory = useDeleteCategory();

  // Handlers
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleSort = (field: "name" | "displayOrder" | "createdAt") => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("asc");
    }
    setCurrentPage(1);
  };

  const handleDelete = async (category: Category) => {
    if (window.confirm(t("categories.confirmDelete"))) {
      try {
        await deleteCategory.mutateAsync(category.id);
      } catch (error) {
        // Error is handled by the mutation
      }
    }
  };

  const getSortIcon = (field: string) => {
    if (sortBy !== field) return null;
    return sortOrder === "asc" ? "↑" : "↓";
  };

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-error-500">
            {t("common.error")}: {error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{t("categories.categoryList")}</CardTitle>
          <Button onClick={onAddCategory} className="flex items-center gap-2">
            <PlusIcon className="w-4 h-4" />
            {t("categories.addCategory")}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Search */}
        <div className="flex flex-col gap-4 mb-6">
          <div className="w-full">
            <Input
              placeholder={t("categories.searchCategories")}
              leftIcon={<MagnifyingGlassIcon className="w-4 h-4" />}
              onChange={e => handleSearchChange(e.target.value)}
            />
          </div>
        </div>

        {/* Table */}
        <div className="rounded-xl bg-neutral-50 dark:bg-neutral-900">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead
                  className="cursor-pointer hover:bg-neutral-50 dark:hover:bg-neutral-800"
                  onClick={() => handleSort("name")}
                >
                  {t("categories.categoryName")} {getSortIcon("name")}
                </TableHead>
                <TableHead>{t("categories.parentCategory")}</TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-neutral-50 dark:hover:bg-neutral-800"
                  onClick={() => handleSort("displayOrder")}
                >
                  {t("categories.displayOrder")} {getSortIcon("displayOrder")}
                </TableHead>
                <TableHead>{t("categories.productCount")}</TableHead>
                <TableHead>{t("common.status")}</TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-neutral-50 dark:hover:bg-neutral-800"
                  onClick={() => handleSort("createdAt")}
                >
                  {t("categories.createdAt")} {getSortIcon("createdAt")}
                </TableHead>
                <TableHead className="text-right">
                  {t("categories.actions")}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    {t("common.loading")}
                  </TableCell>
                </TableRow>
              ) : (categoriesResponse as any)?.categories?.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    {t("categories.noCategoriesFound")}
                  </TableCell>
                </TableRow>
              ) : (
                (categoriesResponse as any)?.categories?.map(
                  (category: any) => (
                    <TableRow key={category.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          {category.color && (
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: category.color }}
                            />
                          )}
                          {category.name}
                        </div>
                      </TableCell>
                      <TableCell>
                        {category.parent?.name || t("categories.noParent")}
                      </TableCell>
                      <TableCell>{category.displayOrder}</TableCell>
                      <TableCell>{category._count?.products || 0}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {category.active ? (
                            <CheckCircleIcon className="w-4 h-4 text-success-500" />
                          ) : (
                            <XCircleIcon className="w-4 h-4 text-error-500" />
                          )}
                          <span
                            className={
                              category.active
                                ? "text-success-700"
                                : "text-error-700"
                            }
                          >
                            {category.active
                              ? t("categories.active")
                              : t("categories.inactive")}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>{formatDate(category.createdAt)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onEditCategory(category)}
                          >
                            <PencilIcon className="w-4 h-4" />
                          </Button>
                          {/* Toggle Status - Temporarily disabled */}
                          {/* <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleStatus(category)}
                          disabled={toggleStatus.isPending}
                        >
                          {category.active ? (
                            <XCircleIcon className="w-4 h-4 text-error-500" />
                          ) : (
                            <CheckCircleIcon className="w-4 h-4 text-success-500" />
                          )}
                        </Button> */}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(category)}
                            disabled={deleteCategory.isPending}
                          >
                            <TrashIcon className="w-4 h-4 text-error-500" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )
                )
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {categoriesResponse?.pagination &&
          categoriesResponse.pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-neutral-500">
                Toplam {categoriesResponse.pagination.total} kategori
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  {t("common.previous")}
                </Button>
                <span className="text-sm">
                  {currentPage} / {categoriesResponse.pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => prev + 1)}
                  disabled={
                    currentPage >= categoriesResponse.pagination.totalPages
                  }
                >
                  {t("common.next")}
                </Button>
              </div>
            </div>
          )}
      </CardContent>
    </Card>
  );
};
