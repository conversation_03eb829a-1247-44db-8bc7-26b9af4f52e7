import { ReceiptPercentIcon, TagIcon } from "@heroicons/react/24/outline";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Settings } from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { CategoryForm } from "../components/categories/CategoryForm";
import { CategoryList } from "../components/categories/CategoryList";
import { ErrorBoundary } from "../components/ErrorBoundary";
import { ModifierGroupManagement } from "../components/ModifierGroups/ModifierGroupManagement";
import { ModifierManagement } from "../components/Modifiers/ModifierManagement";
import { ProductForm } from "../components/products/ProductForm";
import { ProductList } from "../components/products/ProductList";
import { ProductVariantManagement } from "../components/ProductVariants/ProductVariantManagement";
import { TaxForm } from "../components/taxes/TaxForm";
import { TaxList } from "../components/taxes/TaxList";
import { BackButton } from "../components/ui/BackButton";
import { Button } from "../components/ui/Button";
import { LoadingProvider } from "../providers/LoadingProvider";
import { ToastProvider } from "../providers/ToastProvider";
import type { Category, ModifierGroup, Product, Tax } from "../types/api";

// Create a query client instance
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

const ProductManagementContent: React.FC = () => {
  const { t } = useTranslation();

  // View state
  const [currentView, setCurrentView] = useState<
    | "products"
    | "categories"
    | "taxes"
    | "variants"
    | "modifierGroups"
    | "modifiers"
  >("products");

  // Additional state for variant and modifier management
  const [selectedProductForVariants, setSelectedProductForVariants] =
    useState<Product | null>(null);
  const [selectedGroupForModifiers, setSelectedGroupForModifiers] =
    useState<ModifierGroup | null>(null);

  // Product modal states
  const [isProductFormOpen, setIsProductFormOpen] = useState(false);
  const [productFormMode, setProductFormMode] = useState<"create" | "edit">(
    "create"
  );
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  // Category modal states
  const [isCategoryFormOpen, setIsCategoryFormOpen] = useState(false);
  const [categoryFormMode, setCategoryFormMode] = useState<"create" | "edit">(
    "create"
  );
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(
    null
  );

  // Tax modal states
  const [isTaxFormOpen, setIsTaxFormOpen] = useState(false);
  const [taxFormMode, setTaxFormMode] = useState<"create" | "edit">("create");
  const [selectedTax, setSelectedTax] = useState<Tax | null>(null);

  // Product handlers
  const handleAddProduct = () => {
    setSelectedProduct(null);
    setProductFormMode("create");
    setIsProductFormOpen(true);
  };

  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product);
    setProductFormMode("edit");
    setIsProductFormOpen(true);
  };

  const handleCloseProductForm = () => {
    setIsProductFormOpen(false);
    setSelectedProduct(null);
  };

  // Category handlers
  const handleAddCategory = () => {
    setSelectedCategory(null);
    setCategoryFormMode("create");
    setIsCategoryFormOpen(true);
  };

  const handleEditCategory = (category: Category) => {
    setSelectedCategory(category);
    setCategoryFormMode("edit");
    setIsCategoryFormOpen(true);
  };

  const handleCloseCategoryForm = () => {
    setIsCategoryFormOpen(false);
    setSelectedCategory(null);
  };

  // Tax handlers
  const handleAddTax = () => {
    setSelectedTax(null);
    setTaxFormMode("create");
    setIsTaxFormOpen(true);
  };

  const handleEditTax = (tax: Tax) => {
    setSelectedTax(tax);
    setTaxFormMode("edit");
    setIsTaxFormOpen(true);
  };

  const handleCloseTaxForm = () => {
    setIsTaxFormOpen(false);
    setSelectedTax(null);
  };

  // Variant handlers
  const handleManageVariants = (product: Product) => {
    setSelectedProductForVariants(product);
    setCurrentView("variants");
  };

  const handleBackFromVariants = () => {
    setSelectedProductForVariants(null);
    setCurrentView("products");
  };

  // Modifier Group handlers
  const handleManageModifierGroups = () => {
    setCurrentView("modifierGroups");
  };

  const handleBackFromModifierGroups = () => {
    setCurrentView("products");
  };

  const handleManageModifiers = (group: ModifierGroup) => {
    setSelectedGroupForModifiers(group);
    setCurrentView("modifiers");
  };

  const handleBackFromModifiers = () => {
    setSelectedGroupForModifiers(null);
    setCurrentView("modifierGroups");
  };

  const getViewTitle = () => {
    switch (currentView) {
      case "categories":
        return t("categories.title");
      case "taxes":
        return t("taxes.title");
      case "variants":
        return t("variants.title");
      case "modifierGroups":
        return t("modifierGroups.title");
      case "modifiers":
        return t("modifiers.title");
      default:
        return t("products.title");
    }
  };

  const getViewSubtitle = () => {
    switch (currentView) {
      case "categories":
        return t("categories.subtitle");
      case "taxes":
        return t("taxes.subtitle");
      case "variants":
        return selectedProductForVariants
          ? `${selectedProductForVariants.name} - ${t("variants.subtitle")}`
          : t("variants.subtitle");
      case "modifierGroups":
        return t("modifierGroups.subtitle");
      case "modifiers":
        return selectedGroupForModifiers
          ? `${selectedGroupForModifiers.name} - ${t("modifiers.subtitle")}`
          : t("modifiers.subtitle");
      default:
        return t("products.subtitle");
    }
  };

  return (
    <div className="electron-layout h-screen w-screen bg-neutral-100 dark:bg-neutral-800 flex flex-col overflow-hidden">
      {/* Header */}
      <header className="bg-white dark:bg-neutral-black shadow-sm flex-shrink-0 z-10">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1 flex items-center gap-4">
              <BackButton
                onClick={
                  currentView === "modifiers"
                    ? handleBackFromModifiers
                    : currentView === "modifierGroups"
                    ? handleBackFromModifierGroups
                    : currentView === "variants"
                    ? handleBackFromVariants
                    : undefined
                }
              />
              <div>
                <h1 className="text-xl sm:text-2xl font-semibold text-neutral-black dark:text-neutral-white truncate">
                  {getViewTitle()}
                </h1>
                <p className="text-sm text-neutral-500 dark:text-neutral-400 mt-1 hidden sm:block">
                  {getViewSubtitle()}
                </p>
              </div>
            </div>

            {/* View Navigation */}
            <div className="flex items-center gap-2">
              <Button
                variant={currentView === "products" ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentView("products")}
              >
                {t("products.title")}
              </Button>
              <Button
                variant={currentView === "categories" ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentView("categories")}
                className="flex items-center gap-2"
              >
                <TagIcon className="w-4 h-4" />
                {t("categories.title")}
              </Button>
              <Button
                variant={currentView === "taxes" ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentView("taxes")}
                className="flex items-center gap-2"
              >
                <ReceiptPercentIcon className="w-4 h-4" />
                {t("taxes.title")}
              </Button>
              <Button
                variant={
                  currentView === "modifierGroups" ? "default" : "outline"
                }
                size="sm"
                onClick={handleManageModifierGroups}
                className="flex items-center gap-2"
              >
                <Settings className="w-4 h-4" />
                {t("modifierGroups.title")}
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 overflow-auto">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
          {currentView === "products" && (
            <ProductList
              onAddProduct={handleAddProduct}
              onEditProduct={handleEditProduct}
              onManageVariants={handleManageVariants}
            />
          )}

          {currentView === "categories" && (
            <CategoryList
              onAddCategory={handleAddCategory}
              onEditCategory={handleEditCategory}
            />
          )}

          {currentView === "taxes" && (
            <TaxList onAddTax={handleAddTax} onEditTax={handleEditTax} />
          )}

          {currentView === "variants" && selectedProductForVariants && (
            <ProductVariantManagement
              productId={selectedProductForVariants.id}
              productName={selectedProductForVariants.name}
              onBack={handleBackFromVariants}
            />
          )}

          {currentView === "modifierGroups" && (
            <ModifierGroupManagement
              onBack={handleBackFromModifierGroups}
              onManageModifiers={handleManageModifiers}
            />
          )}

          {currentView === "modifiers" && selectedGroupForModifiers && (
            <ModifierManagement
              group={selectedGroupForModifiers}
              onBack={handleBackFromModifiers}
            />
          )}
        </div>
      </main>

      {/* Product Form Modal */}
      <ProductForm
        isOpen={isProductFormOpen}
        onClose={handleCloseProductForm}
        product={selectedProduct}
        mode={productFormMode}
      />

      {/* Category Form Modal */}
      <CategoryForm
        isOpen={isCategoryFormOpen}
        onClose={handleCloseCategoryForm}
        category={selectedCategory}
        mode={categoryFormMode}
      />

      {/* Tax Form Modal */}
      <TaxForm
        isOpen={isTaxFormOpen}
        onClose={handleCloseTaxForm}
        tax={selectedTax}
        mode={taxFormMode}
      />
    </div>
  );
};

export const ProductManagement: React.FC = () => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ToastProvider>
          <LoadingProvider>
            <ProductManagementContent />
          </LoadingProvider>
        </ToastProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};
