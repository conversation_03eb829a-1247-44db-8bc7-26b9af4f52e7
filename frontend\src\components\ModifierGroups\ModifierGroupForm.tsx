import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { X } from "lucide-react";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";
import type {
  CreateModifierGroupRequest,
  ModifierGroup,
  UpdateModifierGroupRequest,
} from "../../types/api";
import { Button } from "../ui/Button";
import { Input } from "../ui/Input";
import { Switch } from "../ui/Switch";
import { Textarea } from "../ui/Textarea";

// Validation schema - will be created with translations in component
const createModifierGroupSchema = (t: any) => z
  .object({
    name: z
      .string()
      .min(2, t("validation.nameMinLength", { min: 2 }))
      .max(100, t("validation.nameMaxLength", { max: 100 })),
    description: z.string().default(""),
    minSelection: z
      .number()
      .int()
      .min(0, t("validation.minSelectionNonNegative"))
      .default(0),
    maxSelection: z
      .number()
      .int()
      .min(1, t("validation.maxSelectionMinimum"))
      .default(1),
    required: z.boolean().default(false),
    freeSelection: z
      .number()
      .int()
      .min(0, t("validation.freeSelectionNonNegative"))
      .default(0),
    displayOrder: z
      .number()
      .int()
      .min(0, t("validation.displayOrderNonNegative"))
      .default(0),
    active: z.boolean().default(true),
  })
  .refine(data => data.minSelection <= data.maxSelection, {
    message: t("validation.minSelectionLessOrEqual"),
    path: ["minSelection"],
  })
  .refine(data => data.freeSelection <= data.maxSelection, {
    message: t("validation.freeSelectionLessOrEqual"),
    path: ["freeSelection"],
  });

type ModifierGroupFormData = z.infer<ReturnType<typeof createModifierGroupSchema>>;

interface ModifierGroupFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (
    data: CreateModifierGroupRequest | UpdateModifierGroupRequest
  ) => void;
  group?: ModifierGroup;
  isLoading?: boolean;
}

export const ModifierGroupForm: React.FC<ModifierGroupFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  group,
  isLoading = false,
}) => {
  const { t } = useTranslation();
  const isEditing = !!group;
  const modifierGroupSchema = createModifierGroupSchema(t);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm({
    resolver: zodResolver(modifierGroupSchema),
    defaultValues: {
      name: "",
      description: "",
      minSelection: 0,
      maxSelection: 1,
      required: false,
      freeSelection: 0,
      displayOrder: 0,
      active: true,
    },
  });

  // Form verilerini group'tan doldur
  useEffect(() => {
    if (group) {
      setValue("name", group.name);
      setValue("description", group.description || "");
      setValue("minSelection", group.minSelection);
      setValue("maxSelection", group.maxSelection);
      setValue("required", group.required);
      setValue("freeSelection", group.freeSelection);
      setValue("displayOrder", group.displayOrder);
      setValue("active", group.active);
    } else {
      reset({
        name: "",
        description: "",
        minSelection: 0,
        maxSelection: 1,
        required: false,
        freeSelection: 0,
        displayOrder: 0,
        active: true,
      });
    }
  }, [group, setValue, reset]);

  const handleFormSubmit = (data: ModifierGroupFormData) => {
    onSubmit(data);
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-neutral-black/50 flex items-center justify-center z-50">
      <div className="bg-neutral-white dark:bg-neutral-black rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-200 dark:border-neutral-700">
          <h2 className="text-xl font-semibold text-neutral-black dark:text-neutral-white">
            {isEditing
              ? t("modifierGroups.editGroup")
              : t("modifierGroups.addGroup")}
          </h2>
          <button
            onClick={handleClose}
            className="text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form
          onSubmit={handleSubmit(handleFormSubmit)}
          className="p-6 space-y-6"
        >
          {/* Grup Adı */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
              {t("modifierGroups.groupName")} *
            </label>
            <Input
              {...register("name")}
              placeholder={t("modifierGroups.groupName")}
              error={errors.name?.message}
            />
          </div>

          {/* Açıklama */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
              {t("modifierGroups.description")}
            </label>
            <Textarea
              {...register("description")}
              placeholder={t("modifierGroups.description")}
              rows={3}
            />
          </div>

          {/* Seçim Ayarları */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                {t("modifierGroups.minSelection")}
              </label>
              <Input
                {...register("minSelection", { valueAsNumber: true })}
                type="number"
                min="0"
                placeholder="0"
                error={errors.minSelection?.message}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                {t("modifierGroups.maxSelection")} *
              </label>
              <Input
                {...register("maxSelection", { valueAsNumber: true })}
                type="number"
                min="1"
                placeholder="1"
                error={errors.maxSelection?.message}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                {t("modifierGroups.freeSelection")}
              </label>
              <Input
                {...register("freeSelection", { valueAsNumber: true })}
                type="number"
                min="0"
                placeholder="0"
                error={errors.freeSelection?.message}
              />
            </div>
          </div>

          {/* Görüntüleme Sırası */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
              {t("modifierGroups.displayOrder")}
            </label>
            <Input
              {...register("displayOrder", { valueAsNumber: true })}
              type="number"
              min="0"
              placeholder="0"
              error={errors.displayOrder?.message}
            />
          </div>

          {/* Checkboxes */}
          <div className="space-y-4">
            {/* Zorunlu */}
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                  {t("modifierGroups.required")}
                </label>
                <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                  {t("modifierGroups.requiredDescription", "Bu grup müşteri tarafından seçilmek zorunda")}
                </p>
              </div>
              <Switch
                checked={watch("required") || false}
                onCheckedChange={checked => setValue("required", checked)}
              />
            </div>

            {/* Aktif Durumu */}
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                {t("modifierGroups.active")}
              </label>
              <Switch
                checked={watch("active") || false}
                onCheckedChange={checked => setValue("active", checked)}
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-neutral-200 dark:border-neutral-700">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              {t("common.cancel")}
            </Button>
            <Button
              type="submit"
              disabled={!isValid || isLoading}
            >
              {isEditing ? t("common.save") : t("common.add")}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
