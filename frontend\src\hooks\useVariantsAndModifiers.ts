import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { useToast } from "../providers/ToastProvider";
import { ModifierGroupService } from "../services/modifierGroupService";
import { ModifierService } from "../services/modifierService";
import { ProductVariantService } from "../services/productVariantService";
import type {
  CreateModifierGroupRequest,
  CreateModifierRequest,
  CreateProductVariantRequest,
  ModifierGroupQueryParams,
  ModifierQueryParams,
  ProductVariantQueryParams,
  UpdateModifierGroupRequest,
  UpdateModifierRequest,
  UpdateProductVariantRequest,
} from "../types/api";

// Query Keys
export const productVariantKeys = {
  all: ["productVariants"] as const,
  lists: () => [...productVariantKeys.all, "list"] as const,
  list: (params?: ProductVariantQueryParams) =>
    [...productVariantKeys.lists(), params] as const,
  details: () => [...productVariantKeys.all, "detail"] as const,
  detail: (id: string) => [...productVariantKeys.details(), id] as const,
  byProduct: (productId: string) =>
    [...productVariantKeys.all, "byProduct", productId] as const,
  stats: (productId?: string) =>
    [...productVariantKeys.all, "stats", productId] as const,
};

export const modifierGroupKeys = {
  all: ["modifierGroups"] as const,
  lists: () => [...modifierGroupKeys.all, "list"] as const,
  list: (params?: ModifierGroupQueryParams) =>
    [...modifierGroupKeys.lists(), params] as const,
  details: () => [...modifierGroupKeys.all, "detail"] as const,
  detail: (id: string) => [...modifierGroupKeys.details(), id] as const,
  active: () => [...modifierGroupKeys.all, "active"] as const,
  stats: () => [...modifierGroupKeys.all, "stats"] as const,
  byProduct: (productId: string) =>
    [...modifierGroupKeys.all, "byProduct", productId] as const,
};

export const modifierKeys = {
  all: ["modifiers"] as const,
  lists: () => [...modifierKeys.all, "list"] as const,
  list: (params?: ModifierQueryParams) =>
    [...modifierKeys.lists(), params] as const,
  details: () => [...modifierKeys.all, "detail"] as const,
  detail: (id: string) => [...modifierKeys.details(), id] as const,
  byGroup: (groupId: string) =>
    [...modifierKeys.all, "byGroup", groupId] as const,
  active: (groupId?: string) =>
    [...modifierKeys.all, "active", groupId] as const,
  stats: (groupId?: string) => [...modifierKeys.all, "stats", groupId] as const,
};

// ProductVariant Hooks
export const useProductVariants = (params?: ProductVariantQueryParams) => {
  return useQuery({
    queryKey: productVariantKeys.list(params),
    queryFn: () => ProductVariantService.getProductVariants(params),
    staleTime: 5 * 60 * 1000, // 5 dakika
    gcTime: 10 * 60 * 1000, // 10 dakika
  });
};

export const useProductVariantsByProduct = (
  productId: string,
  params?: Omit<ProductVariantQueryParams, "productId">
) => {
  return useQuery({
    queryKey: productVariantKeys.byProduct(productId),
    queryFn: () =>
      ProductVariantService.getProductVariantsByProduct(productId, params),
    enabled: !!productId,
    staleTime: 5 * 60 * 1000,
  });
};

export const useProductVariant = (id: string) => {
  return useQuery({
    queryKey: productVariantKeys.detail(id),
    queryFn: () => ProductVariantService.getProductVariant(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateProductVariant = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: (data: CreateProductVariantRequest) =>
      ProductVariantService.createProductVariant(data),
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: productVariantKeys.all });
      queryClient.invalidateQueries({
        queryKey: productVariantKeys.byProduct(variables.productId),
      });
      showToast(response.message || t("variants.variantAdded"), "success");
    },
    onError: (error: any) => {
      showToast(error.message || t("variants.createError"), "error");
    },
  });
};

export const useUpdateProductVariant = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: UpdateProductVariantRequest;
    }) => ProductVariantService.updateProductVariant(id, data),
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: productVariantKeys.all });
      queryClient.invalidateQueries({
        queryKey: productVariantKeys.detail(variables.id),
      });
      showToast(response.message || t("variants.variantUpdated"), "success");
    },
    onError: (error: any) => {
      showToast(error.message || t("variants.updateError"), "error");
    },
  });
};

export const useDeleteProductVariant = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: (id: string) => ProductVariantService.deleteProductVariant(id),
    onSuccess: response => {
      queryClient.invalidateQueries({ queryKey: productVariantKeys.all });
      showToast(response.message || t("variants.variantDeleted"), "success");
    },
    onError: (error: any) => {
      showToast(error.message || t("variants.deleteError"), "error");
    },
  });
};

export const useToggleProductVariantStatus = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: (id: string) =>
      ProductVariantService.toggleProductVariantStatus(id),
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: productVariantKeys.all });
      queryClient.invalidateQueries({
        queryKey: productVariantKeys.detail(variables),
      });
      showToast(response.message || t("variants.statusUpdated"), "success");
    },
    onError: (error: any) => {
      showToast(error.message || t("variants.statusUpdateError"), "error");
    },
  });
};

// ModifierGroup Hooks
export const useModifierGroups = (params?: ModifierGroupQueryParams) => {
  return useQuery({
    queryKey: modifierGroupKeys.list(params),
    queryFn: () => ModifierGroupService.getModifierGroups(params),
    staleTime: 10 * 60 * 1000, // 10 dakika
    gcTime: 15 * 60 * 1000, // 15 dakika
  });
};

export const useActiveModifierGroups = () => {
  return useQuery({
    queryKey: modifierGroupKeys.active(),
    queryFn: () => ModifierGroupService.getActiveModifierGroups(),
    staleTime: 15 * 60 * 1000,
  });
};

export const useModifierGroup = (id: string) => {
  return useQuery({
    queryKey: modifierGroupKeys.detail(id),
    queryFn: () => ModifierGroupService.getModifierGroup(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000,
  });
};

export const useCreateModifierGroup = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: (data: CreateModifierGroupRequest) =>
      ModifierGroupService.createModifierGroup(data),
    onSuccess: response => {
      queryClient.invalidateQueries({ queryKey: modifierGroupKeys.all });
      showToast(response.message || t("modifierGroups.groupAdded"), "success");
    },
    onError: (error: any) => {
      showToast(error.message || t("modifierGroups.createError"), "error");
    },
  });
};

export const useUpdateModifierGroup = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: UpdateModifierGroupRequest;
    }) => ModifierGroupService.updateModifierGroup(id, data),
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: modifierGroupKeys.all });
      queryClient.invalidateQueries({
        queryKey: modifierGroupKeys.detail(variables.id),
      });
      showToast(
        response.message || t("modifierGroups.groupUpdated"),
        "success"
      );
    },
    onError: (error: any) => {
      showToast(error.message || t("modifierGroups.updateError"), "error");
    },
  });
};

export const useDeleteModifierGroup = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: (id: string) => ModifierGroupService.deleteModifierGroup(id),
    onSuccess: response => {
      queryClient.invalidateQueries({ queryKey: modifierGroupKeys.all });
      queryClient.invalidateQueries({ queryKey: modifierKeys.all });
      showToast(
        response.message || t("modifierGroups.groupDeleted"),
        "success"
      );
    },
    onError: (error: any) => {
      showToast(error.message || t("modifierGroups.deleteError"), "error");
    },
  });
};

export const useToggleModifierGroupStatus = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: (id: string) =>
      ModifierGroupService.toggleModifierGroupStatus(id),
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: modifierGroupKeys.all });
      queryClient.invalidateQueries({
        queryKey: modifierGroupKeys.detail(variables),
      });
      showToast(
        response.message || t("modifierGroups.statusUpdated"),
        "success"
      );
    },
    onError: (error: any) => {
      showToast(
        error.message || t("modifierGroups.statusUpdateError"),
        "error"
      );
    },
  });
};

// Modifier Hooks
export const useModifiers = (params?: ModifierQueryParams) => {
  return useQuery({
    queryKey: modifierKeys.list(params),
    queryFn: () => ModifierService.getModifiers(params),
    staleTime: 5 * 60 * 1000, // 5 dakika
    gcTime: 10 * 60 * 1000, // 10 dakika
  });
};

export const useModifiersByGroup = (
  groupId: string,
  params?: Omit<ModifierQueryParams, "groupId">
) => {
  return useQuery({
    queryKey: modifierKeys.byGroup(groupId),
    queryFn: () => ModifierService.getModifiersByGroup(groupId, params),
    enabled: !!groupId,
    staleTime: 5 * 60 * 1000,
  });
};

export const useActiveModifiers = (groupId?: string) => {
  return useQuery({
    queryKey: modifierKeys.active(groupId),
    queryFn: () => ModifierService.getActiveModifiers(groupId),
    staleTime: 10 * 60 * 1000,
  });
};

export const useModifier = (id: string) => {
  return useQuery({
    queryKey: modifierKeys.detail(id),
    queryFn: () => ModifierService.getModifier(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateModifier = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: (data: CreateModifierRequest) =>
      ModifierService.createModifier(data),
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: modifierKeys.all });
      queryClient.invalidateQueries({
        queryKey: modifierKeys.byGroup(variables.groupId),
      });
      queryClient.invalidateQueries({
        queryKey: modifierGroupKeys.detail(variables.groupId),
      });
      showToast(response.message || t("modifiers.modifierAdded"), "success");
    },
    onError: (error: any) => {
      showToast(error.message || t("modifiers.createError"), "error");
    },
  });
};

export const useUpdateModifier = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateModifierRequest }) =>
      ModifierService.updateModifier(id, data),
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: modifierKeys.all });
      queryClient.invalidateQueries({
        queryKey: modifierKeys.detail(variables.id),
      });
      showToast(response.message || t("modifiers.modifierUpdated"), "success");
    },
    onError: (error: any) => {
      showToast(error.message || t("modifiers.updateError"), "error");
    },
  });
};

export const useDeleteModifier = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: (id: string) => ModifierService.deleteModifier(id),
    onSuccess: response => {
      queryClient.invalidateQueries({ queryKey: modifierKeys.all });
      showToast(response.message || t("modifiers.modifierDeleted"), "success");
    },
    onError: (error: any) => {
      showToast(error.message || t("modifiers.deleteError"), "error");
    },
  });
};

export const useToggleModifierStatus = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: (id: string) => ModifierService.toggleModifierStatus(id),
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: modifierKeys.all });
      queryClient.invalidateQueries({
        queryKey: modifierKeys.detail(variables),
      });
      showToast(response.message || t("modifiers.statusUpdated"), "success");
    },
    onError: (error: any) => {
      showToast(error.message || t("modifiers.statusUpdateError"), "error");
    },
  });
};
