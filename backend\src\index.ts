import cors from "@fastify/cors";
import helmet from "@fastify/helmet";
import jwt from "@fastify/jwt";
import multipart from "@fastify/multipart";
import fastifyStatic from "@fastify/static";
import websocket from "@fastify/websocket";
import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
import { fastify } from "fastify";
import path from "path";
import { Server } from "socket.io";

// <PERSON><PERSON><PERSON> değ<PERSON>ini yükle
dotenv.config();

// Prisma istemcisini oluştur
export const prisma = new PrismaClient();

// Fastify sunucusunu oluştur
const server = fastify({
  logger: true,
  trustProxy: true,
});

// Middleware'leri kaydet
server.register(cors, {
  origin: true,
  credentials: true,
});

server.register(helmet, {
  contentSecurityPolicy: false,
});

server.register(jwt, {
  secret: process.env.JWT_SECRET || "atropos-super-secret-key",
});

server.register(websocket);

// Multipart desteği (dosya yükleme için)
server.register(multipart, {
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
    files: 1, // Tek seferde 1 dosya
  },
});

// Static dosya servisi (uploads klasörü için)
server.register(fastifyStatic, {
  root: path.join(__dirname, "..", "uploads"),
  prefix: "/uploads/",
});

// Auth plugin'i kaydet
server.register(import("./plugins/auth"));

// API rotalarını kaydet
server.register(import("./routes/auth"), { prefix: "/api/auth" });
server.register(import("./routes/products"), { prefix: "/api/products" });
server.register(import("./routes/categories"), { prefix: "/api/categories" });
server.register(import("./routes/taxes"), { prefix: "/api/taxes" });
server.register(import("./routes/productVariants"), {
  prefix: "/api/product-variants",
});
server.register(import("./routes/modifierGroups"), {
  prefix: "/api/modifier-groups",
});
server.register(import("./routes/modifiers"), { prefix: "/api/modifiers" });

// Socket.io kurulumu
const io = new Server(server.server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
  },
});

// Socket.io olaylarını dinle
io.on("connection", socket => {
  console.log("Client connected:", socket.id);

  socket.on("join:branch", branchId => {
    socket.join(`branch:${branchId}`);
    console.log(`Socket ${socket.id} joined branch:${branchId}`);
  });

  socket.on("join:kitchen", branchId => {
    socket.join(`kitchen:${branchId}`);
    console.log(`Socket ${socket.id} joined kitchen:${branchId}`);
  });

  socket.on("disconnect", () => {
    console.log("Client disconnected:", socket.id);
  });
});

// Global hata işleyici
server.setErrorHandler((error, request, reply) => {
  server.log.error(error);
  reply.status(500).send({
    success: false,
    error: "Internal Server Error",
    message:
      process.env.NODE_ENV === "production" ? "Bir hata oluştu" : error.message,
  });
});

// Sunucuyu başlat
const start = async () => {
  try {
    const port = process.env.PORT ? parseInt(process.env.PORT) : 3000;
    const host = process.env.HOST || "0.0.0.0";

    await server.listen({ port, host });
    console.log(`Server running at http://${host}:${port}`);
  } catch (err) {
    server.log.error(err);
    process.exit(1);
  }
};

// Veritabanı bağlantısını kapat
const closeGracefully = async (signal: string) => {
  console.log(`Received signal to terminate: ${signal}`);

  await server.close();
  await prisma.$disconnect();
  process.exit(0);
};

process.on("SIGINT", () => closeGracefully("SIGINT"));
process.on("SIGTERM", () => closeGracefully("SIGTERM"));

start();
