import { apiClient } from "../lib/api";
import type {
  ApiResponse,
  Category,
  CategoryQueryParams,
  CreateCategoryRequest,
  PaginatedResponse,
  UpdateCategoryRequest,
} from "../types/api";

export class CategoryService {
  private static readonly BASE_PATH = "/categories";

  // Kategorileri listele
  static async getCategories(
    params?: CategoryQueryParams
  ): Promise<ApiResponse<PaginatedResponse<Category>>> {
    const searchParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
    }

    const queryString = searchParams.toString();
    const endpoint = queryString
      ? `${this.BASE_PATH}?${queryString}`
      : this.BASE_PATH;

    return apiClient.get<PaginatedResponse<Category>>(endpoint);
  }

  // Hiyerarşik kategori listesi (tüm kategoriler)
  static async getCategoriesHierarchy(): Promise<ApiResponse<Category[]>> {
    return apiClient.get<Category[]>(`${this.BASE_PATH}?includeHierarchy=true`);
  }

  // Ana kategoriler (parentId null olanlar)
  static async getRootCategories(): Promise<ApiResponse<Category[]>> {
    return apiClient.get<Category[]>(
      `${this.BASE_PATH}?parentId=null&includeHierarchy=true`
    );
  }

  // Belirli bir kategorinin alt kategorileri
  static async getSubCategories(
    parentId: string
  ): Promise<ApiResponse<Category[]>> {
    return apiClient.get<Category[]>(`${this.BASE_PATH}?parentId=${parentId}`);
  }

  // Tek kategori detayı
  static async getCategory(id: string): Promise<ApiResponse<Category>> {
    return apiClient.get<Category>(`${this.BASE_PATH}/${id}`);
  }

  // Yeni kategori oluştur
  static async createCategory(
    data: CreateCategoryRequest
  ): Promise<ApiResponse<Category>> {
    return apiClient.post<Category>(this.BASE_PATH, data);
  }

  // Kategori güncelle
  static async updateCategory(
    id: string,
    data: UpdateCategoryRequest
  ): Promise<ApiResponse<Category>> {
    return apiClient.put<Category>(`${this.BASE_PATH}/${id}`, data);
  }

  // Kategori sil
  static async deleteCategory(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`${this.BASE_PATH}/${id}`);
  }

  // Kategori adının benzersizliğini kontrol et
  static async checkNameUniqueness(
    name: string,
    parentId?: string,
    excludeId?: string
  ): Promise<ApiResponse<{ isUnique: boolean }>> {
    const params = new URLSearchParams({ name });
    if (parentId) {
      params.append("parentId", parentId);
    }
    if (excludeId) {
      params.append("excludeId", excludeId);
    }
    return apiClient.get<{ isUnique: boolean }>(
      `${this.BASE_PATH}/check-name?${params.toString()}`
    );
  }

  // Kategori durumunu değiştir (aktif/pasif)
  static async toggleCategoryStatus(
    id: string
  ): Promise<ApiResponse<Category>> {
    return apiClient.put<Category>(`${this.BASE_PATH}/${id}/toggle-status`, {});
  }

  // Kategori görüntüleme sırasını güncelle
  static async updateDisplayOrder(
    id: string,
    displayOrder: number
  ): Promise<ApiResponse<Category>> {
    return apiClient.put<Category>(`${this.BASE_PATH}/${id}/display-order`, {
      displayOrder,
    });
  }

  // Kategori sırasını değiştir (drag & drop için)
  static async reorderCategories(
    categoryOrders: { id: string; displayOrder: number }[]
  ): Promise<ApiResponse<{ updated: number }>> {
    return apiClient.put<{ updated: number }>(`${this.BASE_PATH}/reorder`, {
      categoryOrders,
    });
  }

  // Kategoriyi başka bir kategorinin altına taşı
  static async moveCategory(
    id: string,
    newParentId?: string
  ): Promise<ApiResponse<Category>> {
    return apiClient.put<Category>(`${this.BASE_PATH}/${id}/move`, {
      parentId: newParentId,
    });
  }

  // Toplu kategori işlemleri
  static async bulkUpdateStatus(
    ids: string[],
    active: boolean
  ): Promise<ApiResponse<{ updated: number }>> {
    return apiClient.put<{ updated: number }>(`${this.BASE_PATH}/bulk/status`, {
      ids,
      active,
    });
  }

  static async bulkDelete(
    ids: string[]
  ): Promise<ApiResponse<{ deleted: number }>> {
    return apiClient.post<{ deleted: number }>(
      `${this.BASE_PATH}/bulk/delete`,
      { ids }
    );
  }

  // Kategori istatistikleri
  static async getCategoryStats(): Promise<
    ApiResponse<{
      total: number;
      active: number;
      inactive: number;
      withProducts: number;
      withoutProducts: number;
    }>
  > {
    return apiClient.get<{
      total: number;
      active: number;
      inactive: number;
      withProducts: number;
      withoutProducts: number;
    }>(`${this.BASE_PATH}/stats`);
  }

  // Kategori ağacı (breadcrumb için)
  static async getCategoryPath(id: string): Promise<ApiResponse<Category[]>> {
    return apiClient.get<Category[]>(`${this.BASE_PATH}/${id}/path`);
  }

  // Kategori renk önerileri
  static async getColorSuggestions(): Promise<ApiResponse<string[]>> {
    return apiClient.get<string[]>(`${this.BASE_PATH}/color-suggestions`);
  }

  // Kategori ikon önerileri
  static async getIconSuggestions(): Promise<ApiResponse<string[]>> {
    return apiClient.get<string[]>(`${this.BASE_PATH}/icon-suggestions`);
  }

  // Kategoriye ait ürün sayısını al
  static async getProductCount(
    id: string
  ): Promise<ApiResponse<{ count: number }>> {
    return apiClient.get<{ count: number }>(
      `${this.BASE_PATH}/${id}/product-count`
    );
  }

  // Kategori arama önerileri
  static async getSearchSuggestions(
    query: string
  ): Promise<ApiResponse<string[]>> {
    return apiClient.get<string[]>(
      `${this.BASE_PATH}/search-suggestions?q=${encodeURIComponent(query)}`
    );
  }
}
