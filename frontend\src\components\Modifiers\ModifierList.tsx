import {
  Edit,
  Eye,
  MoreVertical,
  Package,
  ToggleLeft,
  ToggleRight,
  Trash2,
} from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { formatCurrency, formatDate } from "../../lib/utils";
import type { Modifier } from "../../types/api";
import { Badge } from "../ui/Badge";
import { Button } from "../ui/Button";
import { ConfirmDialog } from "../ui/ConfirmDialog";
import { DropdownMenu } from "../ui/DropdownMenu";
import { Input } from "../ui/Input";

interface ModifierListProps {
  modifiers: Modifier[];
  isLoading?: boolean;
  onEdit: (modifier: Modifier) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string) => void;
  onView?: (modifier: Modifier) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  showGroupInfo?: boolean;
}

export const ModifierList: React.FC<ModifierListProps> = ({
  modifiers,
  isLoading = false,
  onEdit,
  onDelete,
  onToggleStatus,
  onView,
  searchQuery,
  onSearchChange,
  showGroupInfo = true,
}) => {
  const { t } = useTranslation();
  const [deleteConfirm, setDeleteConfirm] = useState<{
    isOpen: boolean;
    modifierId: string;
    modifierName: string;
  }>({
    isOpen: false,
    modifierId: "",
    modifierName: "",
  });

  const handleDeleteClick = (modifier: Modifier) => {
    setDeleteConfirm({
      isOpen: true,
      modifierId: modifier.id,
      modifierName: modifier.name,
    });
  };

  const handleDeleteConfirm = () => {
    onDelete(deleteConfirm.modifierId);
    setDeleteConfirm({ isOpen: false, modifierId: "", modifierName: "" });
  };

  const handleDeleteCancel = () => {
    setDeleteConfirm({ isOpen: false, modifierId: "", modifierName: "" });
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, index) => (
          <div
            key={index}
            className="bg-neutral-white dark:bg-neutral-800 rounded-lg p-6 animate-pulse"
          >
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-48"></div>
                <div className="h-3 bg-neutral-200 dark:bg-neutral-700 rounded w-32"></div>
              </div>
              <div className="h-8 bg-neutral-200 dark:bg-neutral-700 rounded w-24"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (modifiers.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-neutral-400 dark:text-neutral-500 text-lg mb-2">
          {searchQuery
            ? t("modifiers.noModifiersFound")
            : t("modifiers.noModifiersFound")}
        </div>
        {searchQuery && (
          <p className="text-neutral-500 dark:text-neutral-400 text-sm">
            {t("common.noResultsFor", { query: searchQuery })}
          </p>
        )}
      </div>
    );
  }

  return (
    <>
      {/* Search */}
      <div className="mb-6">
        <Input
          placeholder={t("modifiers.searchModifiers")}
          value={searchQuery}
          onChange={e => onSearchChange(e.target.value)}
          className="bg-[#292C2D] dark:bg-[#292C2D] max-w-md"
        />
      </div>

      {/* Modifier Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {modifiers.map(modifier => (
          <div
            key={modifier.id}
            className="bg-neutral-white dark:bg-neutral-800 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
          >
            <div className="p-6">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-neutral-black dark:text-neutral-white mb-1">
                    {modifier.name}
                  </h3>
                  {showGroupInfo && modifier.group && (
                    <p className="text-sm text-neutral-500 dark:text-neutral-400">
                      {modifier.group.name}
                    </p>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={modifier.active ? "success" : "secondary"}
                    size="sm"
                  >
                    {modifier.active
                      ? t("modifiers.active")
                      : t("modifiers.inactive")}
                  </Badge>
                  <DropdownMenu
                    trigger={
                      <Button variant="ghost" size="sm">
                        <MoreVertical size={16} />
                      </Button>
                    }
                    items={[
                      ...(onView
                        ? [
                            {
                              label: t("common.view"),
                              icon: Eye,
                              onClick: () => onView(modifier),
                            },
                          ]
                        : []),
                      {
                        label: t("common.edit"),
                        icon: Edit,
                        onClick: () => onEdit(modifier),
                      },
                      {
                        label: modifier.active
                          ? t("common.deactivate")
                          : t("common.activate"),
                        icon: modifier.active ? ToggleLeft : ToggleRight,
                        onClick: () => onToggleStatus(modifier.id),
                      },
                      {
                        label: t("common.delete"),
                        icon: Trash2,
                        onClick: () => handleDeleteClick(modifier),
                        className: "text-red-600 dark:text-red-400",
                      },
                    ]}
                  />
                </div>
              </div>

              {/* Details */}
              <div className="space-y-3">
                {/* Price */}
                <div className="flex justify-between items-center">
                  <span className="text-sm text-neutral-500 dark:text-neutral-400">
                    {t("modifiers.price")}:
                  </span>
                  <span className="text-lg font-semibold text-green-600 dark:text-green-400">
                    {modifier.price > 0
                      ? formatCurrency(modifier.price)
                      : "Ücretsiz"}
                  </span>
                </div>

                {/* Max Quantity */}
                <div className="flex justify-between items-center">
                  <span className="text-sm text-neutral-500 dark:text-neutral-400">
                    {t("modifiers.maxQuantity")}:
                  </span>
                  <span className="text-sm text-neutral-600 dark:text-neutral-300">
                    {modifier.maxQuantity}
                  </span>
                </div>

                {/* Inventory Item */}
                {modifier.inventoryItem && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-neutral-500 dark:text-neutral-400 flex items-center">
                      <Package size={14} className="mr-1" />
                      {t("modifiers.inventoryItem")}:
                    </span>
                    <span className="text-sm text-neutral-600 dark:text-neutral-300">
                      {modifier.inventoryItem.name}
                    </span>
                  </div>
                )}

                {/* Stock Info */}
                {modifier.inventoryItem && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-neutral-500 dark:text-neutral-400">
                      {t("modifiers.stock")}:
                    </span>
                    <span className="text-sm text-neutral-600 dark:text-neutral-300">
                      {modifier.inventoryItem.currentStock}{" "}
                      {modifier.inventoryItem.unit}
                    </span>
                  </div>
                )}

                {/* Group Info (if showing group info) */}
                {showGroupInfo && modifier.group && (
                  <div className="pt-3 border-t border-neutral-200 dark:border-neutral-700">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-neutral-500 dark:text-neutral-400">
                        {t("modifiers.groupSettings")}:
                      </span>
                      <div className="flex space-x-2">
                        <Badge
                          variant={
                            modifier.group.required ? "warning" : "secondary"
                          }
                          size="xs"
                        >
                          {modifier.group.required
                            ? t("modifierGroups.required")
                            : t("modifierGroups.optional")}
                        </Badge>
                        <span className="text-xs text-neutral-500 dark:text-neutral-400">
                          {modifier.group.minSelection}-
                          {modifier.group.maxSelection}
                        </span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Created Date */}
                <div className="flex justify-between items-center text-xs text-neutral-400 dark:text-neutral-500">
                  <span>{t("modifiers.createdAt")}:</span>
                  <span>{formatDate(modifier.createdAt)}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={deleteConfirm.isOpen}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        title={t("modifiers.deleteConfirmation")}
        message={t("modifiers.confirmDelete")}
        confirmText={t("common.delete")}
        cancelText={t("common.cancel")}
        variant="danger"
      />
    </>
  );
};
