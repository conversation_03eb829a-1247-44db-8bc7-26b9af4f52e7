import { apiClient } from "../lib/api";
import type {
  ApiResponse,
  CreateModifierRequest,
  Modifier,
  ModifierQueryParams,
  PaginatedResponse,
  UpdateModifierRequest,
} from "../types/api";

export class ModifierService {
  private static readonly BASE_PATH = "/modifiers";

  // Modifiyeleri listele
  static async getModifiers(
    params?: ModifierQueryParams
  ): Promise<ApiResponse<PaginatedResponse<Modifier>>> {
    const searchParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
    }

    const queryString = searchParams.toString();
    const endpoint = queryString
      ? `${this.BASE_PATH}?${queryString}`
      : this.BASE_PATH;

    return apiClient.get<PaginatedResponse<Modifier>>(endpoint);
  }

  // Belirli bir grubun modifiyelerini getir
  static async getModifiersByGroup(
    groupId: string,
    params?: Omit<ModifierQueryParams, "groupId">
  ): Promise<ApiResponse<PaginatedResponse<Modifier>>> {
    return this.getModifiers({ ...params, groupId });
  }

  // Tüm aktif modifiyeler (select için)
  static async getActiveModifiers(
    groupId?: string
  ): Promise<ApiResponse<Modifier[]>> {
    const params = groupId
      ? `?active=true&groupId=${groupId}&limit=100`
      : `?active=true&limit=100`;
    return apiClient.get<Modifier[]>(`${this.BASE_PATH}${params}`);
  }

  // Tek modifiye detayı
  static async getModifier(id: string): Promise<ApiResponse<Modifier>> {
    return apiClient.get<Modifier>(`${this.BASE_PATH}/${id}`);
  }

  // Yeni modifiye oluştur
  static async createModifier(
    data: CreateModifierRequest
  ): Promise<ApiResponse<Modifier>> {
    return apiClient.post<Modifier>(this.BASE_PATH, data);
  }

  // Modifiye güncelle
  static async updateModifier(
    id: string,
    data: UpdateModifierRequest
  ): Promise<ApiResponse<Modifier>> {
    return apiClient.put<Modifier>(`${this.BASE_PATH}/${id}`, data);
  }

  // Modifiye sil
  static async deleteModifier(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`${this.BASE_PATH}/${id}`);
  }

  // Modifiye durumunu değiştir (aktif/pasif)
  static async toggleModifierStatus(
    id: string
  ): Promise<ApiResponse<Modifier>> {
    return apiClient.put<Modifier>(`${this.BASE_PATH}/${id}/toggle-status`, {});
  }

  // Modifiye adının benzersizliğini kontrol et (grup içinde)
  static async checkNameUniqueness(
    groupId: string,
    name: string,
    excludeId?: string
  ): Promise<ApiResponse<{ isUnique: boolean }>> {
    const params = new URLSearchParams({ groupId, name });
    if (excludeId) {
      params.append("excludeId", excludeId);
    }
    return apiClient.get<{ isUnique: boolean }>(
      `${this.BASE_PATH}/check-name?${params.toString()}`
    );
  }

  // Modifiye istatistikleri
  static async getModifierStats(groupId?: string): Promise<
    ApiResponse<{
      total: number;
      active: number;
      inactive: number;
      withInventory: number;
      withoutInventory: number;
      averagePrice: number;
      minPrice: number;
      maxPrice: number;
      topSellingModifiers: {
        id: string;
        name: string;
        selectionCount: number;
        revenue: number;
      }[];
    }>
  > {
    const params = groupId ? `?groupId=${groupId}` : "";
    return apiClient.get<{
      total: number;
      active: number;
      inactive: number;
      withInventory: number;
      withoutInventory: number;
      averagePrice: number;
      minPrice: number;
      maxPrice: number;
      topSellingModifiers: {
        id: string;
        name: string;
        selectionCount: number;
        revenue: number;
      }[];
    }>(`${this.BASE_PATH}/stats${params}`);
  }

  // Modifiye sıralamasını güncelle
  static async updateModifierOrder(
    modifierOrders: { id: string; displayOrder: number }[]
  ): Promise<ApiResponse<void>> {
    return apiClient.put<void>(`${this.BASE_PATH}/reorder`, {
      modifierOrders,
    });
  }

  // Toplu modifiye oluşturma
  static async createBulkModifiers(
    groupId: string,
    modifiers: Omit<CreateModifierRequest, "groupId">[]
  ): Promise<ApiResponse<Modifier[]>> {
    return apiClient.post<Modifier[]>(`${this.BASE_PATH}/bulk`, {
      groupId,
      modifiers,
    });
  }

  // Modifiye kopyalama
  static async duplicateModifier(
    id: string,
    newName: string,
    targetGroupId?: string
  ): Promise<ApiResponse<Modifier>> {
    return apiClient.post<Modifier>(`${this.BASE_PATH}/${id}/duplicate`, {
      name: newName,
      targetGroupId,
    });
  }

  // Modifiye fiyat geçmişi
  static async getModifierPriceHistory(id: string): Promise<
    ApiResponse<
      {
        id: string;
        price: number;
        updatedAt: string;
        updatedBy?: string;
      }[]
    >
  > {
    return apiClient.get<
      {
        id: string;
        price: number;
        updatedAt: string;
        updatedBy?: string;
      }[]
    >(`${this.BASE_PATH}/${id}/price-history`);
  }

  // Modifiye stok durumu (envanter öğesi varsa)
  static async getModifierStock(id: string): Promise<
    ApiResponse<{
      currentStock: number;
      reservedStock: number;
      availableStock: number;
      criticalLevel?: number;
      isLowStock: boolean;
      isOutOfStock: boolean;
      unit: string;
    }>
  > {
    return apiClient.get<{
      currentStock: number;
      reservedStock: number;
      availableStock: number;
      criticalLevel?: number;
      isLowStock: boolean;
      isOutOfStock: boolean;
      unit: string;
    }>(`${this.BASE_PATH}/${id}/stock`);
  }

  // Modifiye satış istatistikleri
  static async getModifierSalesStats(
    id: string,
    startDate?: string,
    endDate?: string
  ): Promise<
    ApiResponse<{
      totalSelected: number;
      totalRevenue: number;
      averageQuantity: number;
      topSellingDays: string[];
      salesTrend: { date: string; quantity: number; revenue: number }[];
      popularCombinations: {
        modifierIds: string[];
        modifierNames: string[];
        combinationCount: number;
      }[];
    }>
  > {
    const params = new URLSearchParams();
    if (startDate) params.append("startDate", startDate);
    if (endDate) params.append("endDate", endDate);

    const queryString = params.toString();
    const endpoint = queryString
      ? `${this.BASE_PATH}/${id}/sales-stats?${queryString}`
      : `${this.BASE_PATH}/${id}/sales-stats`;

    return apiClient.get<{
      totalSelected: number;
      totalRevenue: number;
      averageQuantity: number;
      topSellingDays: string[];
      salesTrend: { date: string; quantity: number; revenue: number }[];
      popularCombinations: {
        modifierIds: string[];
        modifierNames: string[];
        combinationCount: number;
      }[];
    }>(endpoint);
  }

  // Modifiye dışa aktarma
  static async exportModifiers(
    params?: ModifierQueryParams,
    format: "csv" | "xlsx" = "xlsx"
  ): Promise<ApiResponse<{ downloadUrl: string }>> {
    const searchParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
    }

    searchParams.append("format", format);

    return apiClient.get<{ downloadUrl: string }>(
      `${this.BASE_PATH}/export?${searchParams.toString()}`
    );
  }

  // Modifiye içe aktarma
  static async importModifiers(
    file: File,
    groupId: string
  ): Promise<
    ApiResponse<{
      imported: number;
      failed: number;
      errors: string[];
    }>
  > {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("groupId", groupId);

    return apiClient.post<{
      imported: number;
      failed: number;
      errors: string[];
    }>(`${this.BASE_PATH}/import`, formData);
  }

  // Envanter öğesi ile modifiye eşleştirme
  static async linkToInventoryItem(
    id: string,
    inventoryItemId: string
  ): Promise<ApiResponse<Modifier>> {
    return apiClient.put<Modifier>(`${this.BASE_PATH}/${id}/link-inventory`, {
      inventoryItemId,
    });
  }

  // Envanter öğesi ile modifiye eşleştirmesini kaldır
  static async unlinkFromInventoryItem(
    id: string
  ): Promise<ApiResponse<Modifier>> {
    return apiClient.put<Modifier>(
      `${this.BASE_PATH}/${id}/unlink-inventory`,
      {}
    );
  }

  // Modifiye önerileri (AI tabanlı)
  static async getModifierSuggestions(
    groupId: string,
    productCategory?: string
  ): Promise<
    ApiResponse<
      {
        name: string;
        suggestedPrice: number;
        reason: string;
        confidence: number;
      }[]
    >
  > {
    const params = new URLSearchParams({ groupId });
    if (productCategory) params.append("productCategory", productCategory);

    return apiClient.get<
      {
        name: string;
        suggestedPrice: number;
        reason: string;
        confidence: number;
      }[]
    >(`${this.BASE_PATH}/suggestions?${params.toString()}`);
  }
}
