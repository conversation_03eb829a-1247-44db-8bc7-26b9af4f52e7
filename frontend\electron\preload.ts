import { contextBridge, ipcRenderer } from "electron";

// Güvenli bir şekilde ana süreç ile iletişim kurmak için API
contextBridge.exposeInMainWorld("electronAPI", {
  // Örnek: Ana süreçten veri almak
  getAppVersion: () => ipcRenderer.invoke("get-app-version"),

  // Örnek: Ana sürece veri göndermek
  printReceipt: (data: any) => ipcRenderer.invoke("print-receipt", data),

  // Örnek: <PERSON><PERSON>
  onPrinterStatus: (callback: (status: string) => void) => {
    ipcRenderer.on("printer-status", (_event, status) => callback(status));
    return () => {
      ipcRenderer.removeAllListeners("printer-status");
    };
  },
});
