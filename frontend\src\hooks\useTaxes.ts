import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { useToast } from "../providers/ToastProvider";
import { TaxService } from "../services/taxService";
import type {
  CreateTaxRequest,
  TaxQueryParams,
  UpdateTaxRequest,
} from "../types/api";

// Query keys
export const taxKeys = {
  all: ["taxes"] as const,
  lists: () => [...taxKeys.all, "list"] as const,
  list: (params?: TaxQueryParams) => [...taxKeys.lists(), params] as const,
  active: () => [...taxKeys.all, "active"] as const,
  details: () => [...taxKeys.all, "detail"] as const,
  detail: (id: string) => [...taxKeys.details(), id] as const,
  stats: () => [...taxKeys.all, "stats"] as const,
};

// Tax Queries
export const useTaxes = (params?: TaxQueryParams) => {
  return useQuery({
    queryKey: taxKeys.list(params),
    queryFn: () => TaxService.getTaxes(params),
    select: response => response.data,
  });
};

export const useActiveTaxes = () => {
  return useQuery({
    queryKey: taxKeys.active(),
    queryFn: () => TaxService.getActiveTaxes(),
    select: response => response.data,
  });
};

export const useTax = (id: string) => {
  return useQuery({
    queryKey: taxKeys.detail(id),
    queryFn: () => TaxService.getTax(id),
    select: response => response.data,
    enabled: !!id,
  });
};

export const useDefaultTax = () => {
  return useQuery({
    queryKey: [...taxKeys.all, "default"],
    queryFn: () => TaxService.getDefaultTax(),
    select: response => response.data,
  });
};

// Tax Mutations
export const useCreateTax = () => {
  const queryClient = useQueryClient();
  const { success, error } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (data: CreateTaxRequest) => TaxService.createTax(data),
    onSuccess: response => {
      queryClient.invalidateQueries({ queryKey: taxKeys.all });
      success({
        title: t("taxes.taxAdded"),
        message: response.message,
      });
    },
    onError: (err: any) => {
      error({
        title: t("common.error"),
        message: err.message || t("taxes.createError"),
      });
    },
  });
};

export const useUpdateTax = () => {
  const queryClient = useQueryClient();
  const { success, error } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTaxRequest }) =>
      TaxService.updateTax(id, data),
    onSuccess: (response, { id }) => {
      queryClient.invalidateQueries({ queryKey: taxKeys.all });
      queryClient.invalidateQueries({ queryKey: taxKeys.detail(id) });
      success({
        title: t("taxes.taxUpdated"),
        message: response.message,
      });
    },
    onError: (err: any) => {
      error({
        title: t("common.error"),
        message: err.message || t("taxes.updateError"),
      });
    },
  });
};

export const useDeleteTax = () => {
  const queryClient = useQueryClient();
  const { success, error } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => TaxService.deleteTax(id),
    onSuccess: response => {
      queryClient.invalidateQueries({ queryKey: taxKeys.all });
      success({
        title: t("taxes.taxDeleted"),
        message: response.message,
      });
    },
    onError: (err: any) => {
      error({
        title: t("common.error"),
        message: err.message || t("taxes.deleteError"),
      });
    },
  });
};

export const useToggleTaxStatus = () => {
  const queryClient = useQueryClient();
  const { success, error } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => TaxService.toggleTaxStatus(id),
    onSuccess: (response, id) => {
      queryClient.invalidateQueries({ queryKey: taxKeys.all });
      queryClient.invalidateQueries({ queryKey: taxKeys.detail(id) });
      success({
        title: t("taxes.statusUpdated"),
        message: response.message,
      });
    },
    onError: (err: any) => {
      error({
        title: t("common.error"),
        message: err.message || t("taxes.statusUpdateError"),
      });
    },
  });
};

export const useSetDefaultTax = () => {
  const queryClient = useQueryClient();
  const { success, error } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => TaxService.setDefaultTax(id),
    onSuccess: (response, id) => {
      queryClient.invalidateQueries({ queryKey: taxKeys.all });
      queryClient.invalidateQueries({ queryKey: taxKeys.detail(id) });
      success({
        title: t("taxes.statusUpdated"),
        message: response.message,
      });
    },
    onError: (err: any) => {
      error({
        title: t("common.error"),
        message: err.message || t("taxes.statusUpdateError"),
      });
    },
  });
};

// Utility hooks
// export const useTaxProductCount = (id: string) => {
//   return useQuery({
//     queryKey: [...taxKeys.all, 'productCount', id],
//     queryFn: () => TaxService.getProductCount(id),
//     select: (response) => response.data,
//     enabled: !!id,
//   });
// };

// export const useTaxTemplates = (country: string = 'TR') => {
//   return useQuery({
//     queryKey: [...taxKeys.all, 'templates', country],
//     queryFn: () => TaxService.getTaxTemplates(country),
//     select: (response) => response.data,
//     staleTime: 10 * 60 * 1000, // 10 minutes
//   });
// };

// export const useTaxSearchSuggestions = (query: string) => {
//   return useQuery({
//     queryKey: [...taxKeys.all, 'searchSuggestions', query],
//     queryFn: () => TaxService.getSearchSuggestions(query),
//     select: (response) => response.data,
//     enabled: query.length >= 2,
//     staleTime: 2 * 60 * 1000, // 2 minutes
//   });
// };
