{"name": "@atropos/backend", "version": "1.0.0", "description": "Atropos POS Backend API", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:seed": "tsx src/seed.ts", "format": "prettier --write src/", "format:check": "prettier --check src/"}, "keywords": ["pos", "restaurant", "fastify", "prisma", "postgresql"], "author": "<PERSON>", "license": "MIT", "dependencies": {"@fastify/cors": "^10.0.1", "@fastify/helmet": "^12.0.1", "@fastify/jwt": "^9.0.1", "@fastify/multipart": "^9.0.3", "@fastify/static": "^8.2.0", "@fastify/websocket": "^11.0.1", "@prisma/client": "^6.2.0", "bcryptjs": "^2.4.3", "dotenv": "^16.4.7", "fastify": "^5.2.0", "socket.io": "^4.8.1", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^22.10.5", "prisma": "^6.2.0", "tsx": "^4.19.2", "typescript": "^5.3.3"}}