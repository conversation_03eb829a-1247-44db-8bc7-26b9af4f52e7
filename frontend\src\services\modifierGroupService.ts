import { apiClient } from "../lib/api";
import type {
  ApiResponse,
  CreateModifierGroupRequest,
  ModifierGroup,
  ModifierGroupQueryParams,
  PaginatedResponse,
  UpdateModifierGroupRequest,
} from "../types/api";

export class ModifierGroupService {
  private static readonly BASE_PATH = "/modifier-groups";

  // Modifiye gruplarını listele
  static async getModifierGroups(
    params?: ModifierGroupQueryParams
  ): Promise<ApiResponse<PaginatedResponse<ModifierGroup>>> {
    const searchParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
    }

    const queryString = searchParams.toString();
    const endpoint = queryString
      ? `${this.BASE_PATH}?${queryString}`
      : this.BASE_PATH;

    return apiClient.get<PaginatedResponse<ModifierGroup>>(endpoint);
  }

  // Tüm aktif modifiye grupları (select için)
  static async getActiveModifierGroups(): Promise<
    ApiResponse<ModifierGroup[]>
  > {
    return apiClient.get<ModifierGroup[]>(
      `${this.BASE_PATH}?active=true&limit=100`
    );
  }

  // Tek modifiye grup detayı
  static async getModifierGroup(
    id: string
  ): Promise<ApiResponse<ModifierGroup>> {
    return apiClient.get<ModifierGroup>(`${this.BASE_PATH}/${id}`);
  }

  // Yeni modifiye grup oluştur
  static async createModifierGroup(
    data: CreateModifierGroupRequest
  ): Promise<ApiResponse<ModifierGroup>> {
    return apiClient.post<ModifierGroup>(this.BASE_PATH, data);
  }

  // Modifiye grup güncelle
  static async updateModifierGroup(
    id: string,
    data: UpdateModifierGroupRequest
  ): Promise<ApiResponse<ModifierGroup>> {
    return apiClient.put<ModifierGroup>(`${this.BASE_PATH}/${id}`, data);
  }

  // Modifiye grup sil
  static async deleteModifierGroup(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`${this.BASE_PATH}/${id}`);
  }

  // Modifiye grup durumunu değiştir (aktif/pasif)
  static async toggleModifierGroupStatus(
    id: string
  ): Promise<ApiResponse<ModifierGroup>> {
    return apiClient.put<ModifierGroup>(
      `${this.BASE_PATH}/${id}/toggle-status`,
      {}
    );
  }

  // Modifiye grup adının benzersizliğini kontrol et
  static async checkNameUniqueness(
    name: string,
    excludeId?: string
  ): Promise<ApiResponse<{ isUnique: boolean }>> {
    const params = new URLSearchParams({ name });
    if (excludeId) {
      params.append("excludeId", excludeId);
    }
    return apiClient.get<{ isUnique: boolean }>(
      `${this.BASE_PATH}/check-name?${params.toString()}`
    );
  }

  // Modifiye grup istatistikleri
  static async getModifierGroupStats(): Promise<
    ApiResponse<{
      total: number;
      active: number;
      inactive: number;
      required: number;
      optional: number;
      averageModifiers: number;
      mostUsedGroups: {
        id: string;
        name: string;
        usageCount: number;
      }[];
    }>
  > {
    return apiClient.get<{
      total: number;
      active: number;
      inactive: number;
      required: number;
      optional: number;
      averageModifiers: number;
      mostUsedGroups: {
        id: string;
        name: string;
        usageCount: number;
      }[];
    }>(`${this.BASE_PATH}/stats`);
  }

  // Modifiye grup sıralamasını güncelle
  static async updateModifierGroupOrder(
    groupOrders: { id: string; displayOrder: number }[]
  ): Promise<ApiResponse<void>> {
    return apiClient.put<void>(`${this.BASE_PATH}/reorder`, {
      groupOrders,
    });
  }

  // Modifiye grubunu kopyala
  static async duplicateModifierGroup(
    id: string,
    newName: string
  ): Promise<ApiResponse<ModifierGroup>> {
    return apiClient.post<ModifierGroup>(`${this.BASE_PATH}/${id}/duplicate`, {
      name: newName,
    });
  }

  // Modifiye grubunu ürüne ata
  static async assignToProduct(
    groupId: string,
    productId: string,
    displayOrder?: number
  ): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`${this.BASE_PATH}/${groupId}/assign-product`, {
      productId,
      displayOrder,
    });
  }

  // Modifiye grubunu üründen kaldır
  static async removeFromProduct(
    groupId: string,
    productId: string
  ): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(
      `${this.BASE_PATH}/${groupId}/remove-product/${productId}`
    );
  }

  // Ürüne atanmış modifiye gruplarını getir
  static async getProductModifierGroups(
    productId: string
  ): Promise<ApiResponse<ModifierGroup[]>> {
    return apiClient.get<ModifierGroup[]>(
      `${this.BASE_PATH}/by-product/${productId}`
    );
  }

  // Modifiye grup kullanım istatistikleri
  static async getModifierGroupUsageStats(
    id: string,
    startDate?: string,
    endDate?: string
  ): Promise<
    ApiResponse<{
      totalOrders: number;
      totalRevenue: number;
      averageSelections: number;
      popularModifiers: {
        id: string;
        name: string;
        selectionCount: number;
        revenue: number;
      }[];
      usageTrend: { date: string; orderCount: number; revenue: number }[];
    }>
  > {
    const params = new URLSearchParams();
    if (startDate) params.append("startDate", startDate);
    if (endDate) params.append("endDate", endDate);

    const queryString = params.toString();
    const endpoint = queryString
      ? `${this.BASE_PATH}/${id}/usage-stats?${queryString}`
      : `${this.BASE_PATH}/${id}/usage-stats`;

    return apiClient.get<{
      totalOrders: number;
      totalRevenue: number;
      averageSelections: number;
      popularModifiers: {
        id: string;
        name: string;
        selectionCount: number;
        revenue: number;
      }[];
      usageTrend: { date: string; orderCount: number; revenue: number }[];
    }>(endpoint);
  }

  // Modifiye grup dışa aktarma
  static async exportModifierGroups(
    params?: ModifierGroupQueryParams,
    format: "csv" | "xlsx" = "xlsx"
  ): Promise<ApiResponse<{ downloadUrl: string }>> {
    const searchParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
    }

    searchParams.append("format", format);

    return apiClient.get<{ downloadUrl: string }>(
      `${this.BASE_PATH}/export?${searchParams.toString()}`
    );
  }

  // Modifiye grup içe aktarma
  static async importModifierGroups(file: File): Promise<
    ApiResponse<{
      imported: number;
      failed: number;
      errors: string[];
    }>
  > {
    const formData = new FormData();
    formData.append("file", file);

    return apiClient.post<{
      imported: number;
      failed: number;
      errors: string[];
    }>(`${this.BASE_PATH}/import`, formData);
  }

  // Modifiye grup şablonları
  static async getModifierGroupTemplates(): Promise<
    ApiResponse<
      {
        id: string;
        name: string;
        description: string;
        category: string;
        modifiers: {
          name: string;
          price: number;
          displayOrder: number;
        }[];
      }[]
    >
  > {
    return apiClient.get<
      {
        id: string;
        name: string;
        description: string;
        category: string;
        modifiers: {
          name: string;
          price: number;
          displayOrder: number;
        }[];
      }[]
    >(`${this.BASE_PATH}/templates`);
  }

  // Şablondan modifiye grup oluştur
  static async createFromTemplate(
    templateId: string,
    customizations?: {
      name?: string;
      description?: string;
      modifierPriceMultiplier?: number;
    }
  ): Promise<ApiResponse<ModifierGroup>> {
    return apiClient.post<ModifierGroup>(`${this.BASE_PATH}/from-template`, {
      templateId,
      customizations,
    });
  }
}
