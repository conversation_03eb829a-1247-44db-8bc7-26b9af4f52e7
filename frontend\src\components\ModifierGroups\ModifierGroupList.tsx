import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ToggleLeft,
  ToggleRight,
  Trash2,
} from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { formatDate } from "../../lib/utils";
import type { ModifierGroup } from "../../types/api";
import { Badge } from "../ui/Badge";
import { Button } from "../ui/Button";
import { ConfirmDialog } from "../ui/ConfirmDialog";
import { DropdownMenu } from "../ui/DropdownMenu";
import { Input } from "../ui/Input";

interface ModifierGroupListProps {
  groups: ModifierGroup[];
  isLoading?: boolean;
  onEdit: (group: ModifierGroup) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string) => void;
  onView?: (group: ModifierGroup) => void;
  onManageModifiers?: (group: ModifierGroup) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export const ModifierGroupList: React.FC<ModifierGroupListProps> = ({
  groups,
  isLoading = false,
  onEdit,
  onDelete,
  onToggleStatus,
  onView,
  onManageModifiers,
  searchQuery,
  onSearchChange,
}) => {
  const { t } = useTranslation();
  const [deleteConfirm, setDeleteConfirm] = useState<{
    isOpen: boolean;
    groupId: string;
    groupName: string;
  }>({
    isOpen: false,
    groupId: "",
    groupName: "",
  });

  const handleDeleteClick = (group: ModifierGroup) => {
    setDeleteConfirm({
      isOpen: true,
      groupId: group.id,
      groupName: group.name,
    });
  };

  const handleDeleteConfirm = () => {
    onDelete(deleteConfirm.groupId);
    setDeleteConfirm({ isOpen: false, groupId: "", groupName: "" });
  };

  const handleDeleteCancel = () => {
    setDeleteConfirm({ isOpen: false, groupId: "", groupName: "" });
  };



  return (
    <>
      {/* Search */}
      <div className="mb-6">
        <Input
          placeholder={t("modifierGroups.searchGroups")}
          value={searchQuery}
          onChange={e => onSearchChange(e.target.value)}
          className="bg-[#292C2D] dark:bg-[#292C2D] max-w-md"
        />
      </div>

      {groups.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-neutral-400 dark:text-neutral-500 text-lg mb-2">
            {searchQuery
              ? t("modifierGroups.noGroupsFound")
              : t("modifierGroups.noGroupsFound")}
          </div>
          {searchQuery && (
            <p className="text-neutral-500 dark:text-neutral-400 text-sm">
              {t("common.noResultsFor", { query: searchQuery })}
            </p>
          )}
        </div>
      ) : (
        <>
          {/* Group Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {groups.map(group => (
          <div
            key={group.id}
            className="bg-neutral-white dark:bg-neutral-800 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
          >
            <div className="p-6">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-neutral-black dark:text-neutral-white mb-1">
                    {group.name}
                  </h3>
                  {group.description && (
                    <p className="text-sm text-neutral-500 dark:text-neutral-400 line-clamp-2">
                      {group.description}
                    </p>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={group.active ? "success" : "secondary"}
                    size="sm"
                  >
                    {group.active
                      ? t("modifierGroups.active")
                      : t("modifierGroups.inactive")}
                  </Badge>
                  <DropdownMenu
                    trigger={
                      <Button variant="ghost" size="sm">
                        <MoreVertical size={16} />
                      </Button>
                    }
                    items={[
                      ...(onView
                        ? [
                            {
                              label: t("common.view"),
                              icon: Eye,
                              onClick: () => onView(group),
                            },
                          ]
                        : []),
                      ...(onManageModifiers
                        ? [
                            {
                              label: t("modifiers.manageModifiers"),
                              icon: Settings,
                              onClick: () => onManageModifiers(group),
                            },
                          ]
                        : []),
                      {
                        label: t("common.edit"),
                        icon: Edit,
                        onClick: () => onEdit(group),
                      },
                      {
                        label: group.active
                          ? t("common.deactivate")
                          : t("common.activate"),
                        icon: group.active ? ToggleLeft : ToggleRight,
                        onClick: () => onToggleStatus(group.id),
                      },
                      {
                        label: t("common.delete"),
                        icon: Trash2,
                        onClick: () => handleDeleteClick(group),
                        className: "text-red-600 dark:text-red-400",
                      },
                    ]}
                  />
                </div>
              </div>

              {/* Details */}
              <div className="space-y-3">
                {/* Selection Settings */}
                <div className="grid grid-cols-3 gap-2 text-sm">
                  <div className="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Min
                    </div>
                    <div className="font-semibold text-gray-900 dark:text-white">
                      {group.minSelection}
                    </div>
                  </div>
                  <div className="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Max
                    </div>
                    <div className="font-semibold text-gray-900 dark:text-white">
                      {group.maxSelection}
                    </div>
                  </div>
                  <div className="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Ücretsiz
                    </div>
                    <div className="font-semibold text-gray-900 dark:text-white">
                      {group.freeSelection}
                    </div>
                  </div>
                </div>

                {/* Required Badge */}
                {group.required && (
                  <div className="flex justify-center">
                    <Badge variant="warning" size="sm">
                      {t("modifierGroups.required")}
                    </Badge>
                  </div>
                )}

                {/* Counts */}
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500 dark:text-gray-400">
                    {t("modifierGroups.modifierCount")}:
                  </span>
                  <span className="font-semibold text-gray-900 dark:text-white">
                    {group._count?.modifiers || 0}
                  </span>
                </div>

                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500 dark:text-gray-400">
                    {t("modifierGroups.productCount")}:
                  </span>
                  <span className="font-semibold text-gray-900 dark:text-white">
                    {group._count?.products || 0}
                  </span>
                </div>

                {/* Created Date */}
                <div className="flex justify-between items-center text-xs text-gray-400 dark:text-gray-500 pt-3 border-t border-gray-200 dark:border-gray-700">
                  <span>{t("modifierGroups.createdAt")}:</span>
                  <span>{formatDate(group.createdAt)}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
          </div>
        </>
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={deleteConfirm.isOpen}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        title={t("modifierGroups.deleteConfirmation")}
        message={t("modifierGroups.confirmDelete")}
        confirmText={t("common.delete")}
        cancelText={t("common.cancel")}
        variant="danger"
      />
    </>
  );
};
