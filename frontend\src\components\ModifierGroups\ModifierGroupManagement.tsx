import {
  CheckCircleIcon,
  CogIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  RectangleGroupIcon,
} from "@heroicons/react/24/outline";
import React, { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  useCreateModifierGroup,
  useDeleteModifierGroup,
  useModifierGroups,
  useToggleModifierGroupStatus,
  useUpdateModifierGroup,
} from "../../hooks/useVariantsAndModifiers";
import { debounce } from "../../lib/utils";
import type { ModifierGroup } from "../../types/api";
import { Button } from "../ui/Button";
import { Card, CardContent } from "../ui/Card";
import { ModifierGroupForm } from "./ModifierGroupForm";
import { ModifierGroupList } from "./ModifierGroupList";

interface ModifierGroupManagementProps {
  onBack: () => void;
  onManageModifiers?: (group: ModifierGroup) => void;
}

export const ModifierGroupManagement: React.FC<
  ModifierGroupManagementProps
> = ({ onBack, onManageModifiers }) => {
  const { t } = useTranslation();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingGroup, setEditingGroup] = useState<ModifierGroup | undefined>();
  const [searchTerm, setSearchTerm] = useState("");
  const [searchQuery, setSearchQuery] = useState("");

  // Debounced search
  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchQuery(value);
      }, 300),
    []
  );

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    debouncedSearch(value);
  };

  // API hooks
  const { data: groupsResponse, isLoading } = useModifierGroups({
    search: searchQuery || undefined,
    limit: 50,
  });

  const createGroupMutation = useCreateModifierGroup();
  const updateGroupMutation = useUpdateModifierGroup();
  const deleteGroupMutation = useDeleteModifierGroup();
  const toggleStatusMutation = useToggleModifierGroupStatus();

  const groups = groupsResponse?.data?.data || [];

  const handleAddGroup = () => {
    setEditingGroup(undefined);
    setIsFormOpen(true);
  };

  const handleEditGroup = (group: ModifierGroup) => {
    setEditingGroup(group);
    setIsFormOpen(true);
  };

  const handleFormSubmit = async (data: any) => {
    try {
      if (editingGroup) {
        await updateGroupMutation.mutateAsync({
          id: editingGroup.id,
          data,
        });
      } else {
        await createGroupMutation.mutateAsync(data);
      }
      setIsFormOpen(false);
      setEditingGroup(undefined);
    } catch (error) {
      // Error handling is done in the mutation hooks
    }
  };

  const handleDeleteGroup = async (id: string) => {
    try {
      await deleteGroupMutation.mutateAsync(id);
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  const handleToggleStatus = async (id: string) => {
    try {
      await toggleStatusMutation.mutateAsync(id);
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setEditingGroup(undefined);
  };

  const isFormLoading =
    createGroupMutation.isPending || updateGroupMutation.isPending;

  return (
    <div className="space-y-6">
      {/* Action Bar */}
      <div className="flex items-center justify-between">
        <div className="flex-1" />
        <Button onClick={handleAddGroup} className="flex items-center gap-2">
          <PlusIcon className="w-4 h-4" />
          {t("modifierGroups.addGroup")}
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  {t("modifierGroups.totalGroups")}
                </p>
                <p className="text-2xl font-bold text-neutral-black dark:text-neutral-white">
                  {groups.length}
                </p>
              </div>
              <div className="w-12 h-12 bg-primary-100 dark:bg-primary-500/10 rounded-lg flex items-center justify-center">
                <RectangleGroupIcon className="w-6 h-6 text-primary-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  {t("modifierGroups.activeGroups")}
                </p>
                <p className="text-2xl font-bold text-neutral-black dark:text-neutral-white">
                  {groups.filter(g => g.active).length}
                </p>
              </div>
              <div className="w-12 h-12 bg-success-100 dark:bg-success-500/10 rounded-lg flex items-center justify-center">
                <CheckCircleIcon className="w-6 h-6 text-success-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  {t("modifierGroups.requiredGroups")}
                </p>
                <p className="text-2xl font-bold text-neutral-black dark:text-neutral-white">
                  {groups.filter(g => g.required).length}
                </p>
              </div>
              <div className="w-12 h-12 bg-warning-100 dark:bg-warning-500/10 rounded-lg flex items-center justify-center">
                <ExclamationTriangleIcon className="w-6 h-6 text-warning-700 dark:text-warning-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  {t("modifierGroups.totalModifiers")}
                </p>
                <p className="text-2xl font-bold text-neutral-black dark:text-neutral-white">
                  {groups.reduce(
                    (sum, g) => sum + (g._count?.modifiers || 0),
                    0
                  )}
                </p>
              </div>
              <div className="w-12 h-12 bg-secondary-100 dark:bg-secondary-500/10 rounded-lg flex items-center justify-center">
                <CogIcon className="w-6 h-6 text-neutral-700 dark:text-neutral-300" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Group List */}
      <Card>
        <CardContent className="p-6">
          <ModifierGroupList
            groups={groups}
            isLoading={isLoading}
            onEdit={handleEditGroup}
            onDelete={handleDeleteGroup}
            onToggleStatus={handleToggleStatus}
            onManageModifiers={onManageModifiers}
            searchQuery={searchTerm}
            onSearchChange={handleSearchChange}
          />
        </CardContent>
      </Card>

      {/* Form Modal */}
      <ModifierGroupForm
        isOpen={isFormOpen}
        onClose={handleFormClose}
        onSubmit={handleFormSubmit}
        group={editingGroup}
        isLoading={isFormLoading}
      />
    </div>
  );
};
