import { ArrowLeft, Plus } from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  useCreateProductVariant,
  useDeleteProductVariant,
  useProductVariantsByProduct,
  useToggleProductVariantStatus,
  useUpdateProductVariant,
} from "../../hooks/useVariantsAndModifiers";
import type { ProductVariant } from "../../types/api";
import { Button } from "../ui/Button";
import { ProductVariantForm } from "./ProductVariantForm";
import { ProductVariantList } from "./ProductVariantList";

interface ProductVariantManagementProps {
  productId: string;
  productName: string;
  onBack: () => void;
}

export const ProductVariantManagement: React.FC<
  ProductVariantManagementProps
> = ({ productId, productName, onBack }) => {
  const { t } = useTranslation();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingVariant, setEditingVariant] = useState<
    ProductVariant | undefined
  >();
  const [searchQuery, setSearchQuery] = useState("");

  // API hooks
  const { data: variantsResponse, isLoading } = useProductVariantsByProduct(
    productId,
    {
      search: searchQuery || undefined,
      limit: 50,
    }
  );

  const createVariantMutation = useCreateProductVariant();
  const updateVariantMutation = useUpdateProductVariant();
  const deleteVariantMutation = useDeleteProductVariant();
  const toggleStatusMutation = useToggleProductVariantStatus();

  const variants = variantsResponse?.data?.data || [];

  const handleAddVariant = () => {
    setEditingVariant(undefined);
    setIsFormOpen(true);
  };

  const handleEditVariant = (variant: ProductVariant) => {
    setEditingVariant(variant);
    setIsFormOpen(true);
  };

  const handleFormSubmit = async (data: any) => {
    try {
      if (editingVariant) {
        await updateVariantMutation.mutateAsync({
          id: editingVariant.id,
          data,
        });
      } else {
        await createVariantMutation.mutateAsync({
          ...data,
          productId,
        });
      }
      setIsFormOpen(false);
      setEditingVariant(undefined);
    } catch (error) {
      // Error handling is done in the mutation hooks
    }
  };

  const handleDeleteVariant = async (id: string) => {
    try {
      await deleteVariantMutation.mutateAsync(id);
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  const handleToggleStatus = async (id: string) => {
    try {
      await toggleStatusMutation.mutateAsync(id);
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setEditingVariant(undefined);
  };

  const isFormLoading =
    createVariantMutation.isPending || updateVariantMutation.isPending;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <ArrowLeft size={20} />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t("variants.title")}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {productName} - {t("variants.subtitle")}
            </p>
          </div>
        </div>
        <Button
          onClick={handleAddVariant}
          className="flex items-center space-x-2"
        >
          <Plus size={20} />
          <span>{t("variants.addVariant")}</span>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Toplam Varyant
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {variants.length}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <div className="w-6 h-6 bg-blue-600 rounded"></div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Aktif Varyant
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {variants.filter(v => v.active).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
              <div className="w-6 h-6 bg-green-600 rounded"></div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Ortalama Fiyat
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {variants.length > 0
                  ? `₺${(
                      variants.reduce((sum, v) => sum + v.price, 0) /
                      variants.length
                    ).toFixed(2)}`
                  : "₺0.00"}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
              <div className="w-6 h-6 bg-yellow-600 rounded"></div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                En Yüksek Fiyat
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {variants.length > 0
                  ? `₺${Math.max(...variants.map(v => v.price)).toFixed(2)}`
                  : "₺0.00"}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
              <div className="w-6 h-6 bg-purple-600 rounded"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Variant List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div className="p-6">
          <ProductVariantList
            variants={variants}
            isLoading={isLoading}
            onEdit={handleEditVariant}
            onDelete={handleDeleteVariant}
            onToggleStatus={handleToggleStatus}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
          />
        </div>
      </div>

      {/* Form Modal */}
      <ProductVariantForm
        isOpen={isFormOpen}
        onClose={handleFormClose}
        onSubmit={handleFormSubmit}
        variant={editingVariant}
        productId={productId}
        isLoading={isFormLoading}
      />
    </div>
  );
};
