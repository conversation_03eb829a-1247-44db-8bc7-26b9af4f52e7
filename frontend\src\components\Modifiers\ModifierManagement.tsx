import {
  PlusIcon,
  CogIcon,
  CheckCircleIcon,
  CurrencyDollarIcon,
  ArchiveBoxIcon
} from "@heroicons/react/24/outline";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  useCreateModifier,
  useDeleteModifier,
  useModifiersByGroup,
  useToggleModifierStatus,
  useUpdateModifier,
} from "../../hooks/useVariantsAndModifiers";
import type { Modifier, ModifierGroup } from "../../types/api";
import { Button } from "../ui/Button";
import { Card, CardContent } from "../ui/Card";
import { ModifierForm } from "./ModifierForm";
import { ModifierList } from "./ModifierList";

interface ModifierManagementProps {
  group: ModifierGroup;
  onBack: () => void;
}

export const ModifierManagement: React.FC<ModifierManagementProps> = ({
  group,
  onBack,
}) => {
  const { t } = useTranslation();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingModifier, setEditingModifier] = useState<
    Modifier | undefined
  >();
  const [searchQuery, setSearchQuery] = useState("");

  // API hooks
  const { data: modifiersResponse, isLoading } = useModifiersByGroup(group.id, {
    search: searchQuery || undefined,
    limit: 50,
  });

  const createModifierMutation = useCreateModifier();
  const updateModifierMutation = useUpdateModifier();
  const deleteModifierMutation = useDeleteModifier();
  const toggleStatusMutation = useToggleModifierStatus();

  const modifiers = modifiersResponse?.data?.data || [];

  const handleAddModifier = () => {
    setEditingModifier(undefined);
    setIsFormOpen(true);
  };

  const handleEditModifier = (modifier: Modifier) => {
    setEditingModifier(modifier);
    setIsFormOpen(true);
  };

  const handleFormSubmit = async (data: any) => {
    try {
      if (editingModifier) {
        await updateModifierMutation.mutateAsync({
          id: editingModifier.id,
          data,
        });
      } else {
        await createModifierMutation.mutateAsync({
          ...data,
          groupId: group.id,
        });
      }
      setIsFormOpen(false);
      setEditingModifier(undefined);
    } catch (error) {
      // Error handling is done in the mutation hooks
    }
  };

  const handleDeleteModifier = async (id: string) => {
    try {
      await deleteModifierMutation.mutateAsync(id);
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  const handleToggleStatus = async (id: string) => {
    try {
      await toggleStatusMutation.mutateAsync(id);
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setEditingModifier(undefined);
  };

  const isFormLoading =
    createModifierMutation.isPending || updateModifierMutation.isPending;

  return (
    <div className="space-y-6">
      {/* Action Bar */}
      <div className="flex items-center justify-between">
        <div className="flex-1" />
        <Button
          onClick={handleAddModifier}
          className="flex items-center gap-2"
        >
          <PlusIcon className="w-4 h-4" />
          {t("modifiers.addModifier")}
        </Button>
      </div>

      {/* Group Info Card */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-neutral-500 dark:text-neutral-400">
                {t("modifierGroups.minSelection")}
              </p>
              <p className="text-2xl font-bold text-neutral-black dark:text-neutral-white">
                {group.minSelection}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-neutral-500 dark:text-neutral-400">
                {t("modifierGroups.maxSelection")}
              </p>
              <p className="text-2xl font-bold text-neutral-black dark:text-neutral-white">
                {group.maxSelection}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-neutral-500 dark:text-neutral-400">
                {t("modifierGroups.freeSelection")}
              </p>
              <p className="text-2xl font-bold text-neutral-black dark:text-neutral-white">
                {group.freeSelection}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-neutral-500 dark:text-neutral-400">
                {t("common.status")}
              </p>
              <p className="text-lg font-semibold">
                {group.required ? (
                  <span className="text-warning-600 dark:text-warning-400">
                    {t("modifierGroups.required")}
                  </span>
                ) : (
                  <span className="text-neutral-600 dark:text-neutral-400">
                    {t("modifierGroups.optional")}
                  </span>
                )}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  {t("modifiers.totalModifiers")}
                </p>
                <p className="text-2xl font-bold text-neutral-black dark:text-neutral-white">
                  {modifiers.length}
                </p>
              </div>
              <div className="w-12 h-12 bg-primary-100 dark:bg-primary-500/10 rounded-lg flex items-center justify-center">
                <CogIcon className="w-6 h-6 text-primary-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  {t("modifiers.activeModifiers")}
                </p>
                <p className="text-2xl font-bold text-neutral-black dark:text-neutral-white">
                  {modifiers.filter(m => m.active).length}
                </p>
              </div>
              <div className="w-12 h-12 bg-success-100 dark:bg-success-500/10 rounded-lg flex items-center justify-center">
                <CheckCircleIcon className="w-6 h-6 text-success-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  {t("modifiers.averagePrice")}
                </p>
                <p className="text-2xl font-bold text-neutral-black dark:text-neutral-white">
                  {modifiers.length > 0
                    ? `₺${(
                        modifiers.reduce((sum, m) => sum + m.price, 0) /
                        modifiers.length
                      ).toFixed(2)}`
                    : "₺0.00"}
                </p>
              </div>
              <div className="w-12 h-12 bg-warning-100 dark:bg-warning-500/10 rounded-lg flex items-center justify-center">
                <CurrencyDollarIcon className="w-6 h-6 text-warning-700 dark:text-warning-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  {t("modifiers.stockModifiers")}
                </p>
                <p className="text-2xl font-bold text-neutral-black dark:text-neutral-white">
                  {modifiers.filter(m => m.inventoryItem).length}
                </p>
              </div>
              <div className="w-12 h-12 bg-secondary-100 dark:bg-secondary-500/10 rounded-lg flex items-center justify-center">
                <ArchiveBoxIcon className="w-6 h-6 text-neutral-700 dark:text-neutral-300" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Modifier List */}
      <Card>
        <CardContent className="p-6">
          <ModifierList
            modifiers={modifiers}
            isLoading={isLoading}
            onEdit={handleEditModifier}
            onDelete={handleDeleteModifier}
            onToggleStatus={handleToggleStatus}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            showGroupInfo={false}
          />
        </CardContent>
      </Card>

      {/* Form Modal */}
      <ModifierForm
        isOpen={isFormOpen}
        onClose={handleFormClose}
        onSubmit={handleFormSubmit}
        modifier={editingModifier}
        defaultGroupId={group.id}
        isLoading={isFormLoading}
      />
    </div>
  );
};
