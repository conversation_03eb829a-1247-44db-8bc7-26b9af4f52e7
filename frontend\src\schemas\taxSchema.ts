import { z } from "zod";
import { TaxType } from "../types/api";

const taxFormSchemaDefinition = z.object({
  // Temel bilgiler
  name: z
    .string()
    .min(2, "Vergi adı en az 2 karakter olmalıdır")
    .max(100, "Vergi adı en fazla 100 karakter olabilir")
    .trim(),

  code: z
    .string()
    .min(1, "Vergi kodu zorunludur")
    .max(20, "Vergi kodu en fazla 20 karakter olabilir")
    .trim()
    .regex(
      /^[A-Za-z0-9_-]+$/,
      "Vergi kodu sadece harf, rakam, tire ve alt çizgi içerebilir"
    )
    .transform(val => val.toUpperCase()),

  // Oran bilgileri
  rate: z
    .number()
    .min(0, "Vergi oranı negatif olamaz")
    .max(100, "Vergi oranı %100'den fazla olamaz"),

  // Vergi tipi
  type: z.enum([TaxType.VAT, TaxType.OTV, TaxType.OIV, TaxType.DAMGA]),

  // Durum ayarları
  isDefault: z.boolean().default(false),

  isIncluded: z.boolean().default(true),

  active: z.boolean().default(true),
});

export const taxFormSchema = taxFormSchemaDefinition;
export type TaxFormData = z.infer<typeof taxFormSchema>;

// Güncelleme için kısmi şema
export const updateTaxFormSchema = taxFormSchema.partial();
export type UpdateTaxFormData = z.infer<typeof updateTaxFormSchema>;

// Form varsayılan değerleri
export const getDefaultTaxFormValues = (): TaxFormData => ({
  name: "",
  code: "",
  rate: 0,
  type: TaxType.VAT,
  isDefault: false,
  isIncluded: true,
  active: true,
});

// Vergi verilerini form verilerine dönüştürme
export const taxToFormData = (tax: any): TaxFormData => ({
  name: tax.name || "",
  code: tax.code || "",
  rate: tax.rate || 0,
  type: tax.type || TaxType.VAT,
  isDefault: tax.isDefault !== undefined ? tax.isDefault : false,
  isIncluded: tax.isIncluded !== undefined ? tax.isIncluded : true,
  active: tax.active !== undefined ? tax.active : true,
});
