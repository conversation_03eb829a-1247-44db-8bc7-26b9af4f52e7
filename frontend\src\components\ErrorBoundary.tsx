import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import type { ErrorInfo, ReactNode } from "react";
import { Component } from "react";
import { Button } from "./ui/Button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./ui/Card";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("ErrorBoundary caught an error:", error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-neutral-100 dark:bg-neutral-800 flex items-center justify-center p-4">
          <Card className="max-w-lg w-full">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-error-100 dark:bg-error-500/10 rounded-full">
                  <ExclamationTriangleIcon className="w-6 h-6 text-error-500" />
                </div>
                <CardTitle className="text-error-500">
                  Bir Hata Oluştu
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-neutral-600 dark:text-neutral-400">
                Uygulama beklenmeyen bir hatayla karşılaştı. Lütfen sayfayı
                yenileyin veya tekrar deneyin.
              </p>

              {process.env.NODE_ENV === "development" && this.state.error && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Hata Detayları (Geliştirici Modu)
                  </summary>
                  <div className="bg-neutral-100 dark:bg-neutral-800 p-3 rounded-md text-xs font-mono overflow-auto max-h-40">
                    <div className="text-error-600 dark:text-error-400 font-semibold mb-2">
                      {this.state.error.name}: {this.state.error.message}
                    </div>
                    <div className="text-neutral-600 dark:text-neutral-400 whitespace-pre-wrap">
                      {this.state.error.stack}
                    </div>
                    {this.state.errorInfo && (
                      <div className="mt-3 pt-3 border-t border-neutral-300 dark:border-neutral-600">
                        <div className="text-neutral-600 dark:text-neutral-400 whitespace-pre-wrap">
                          {this.state.errorInfo.componentStack}
                        </div>
                      </div>
                    )}
                  </div>
                </details>
              )}

              <div className="flex space-x-3 pt-4">
                <Button
                  onClick={this.handleReset}
                  variant="outline"
                  className="flex-1"
                >
                  Tekrar Dene
                </Button>
                <Button onClick={this.handleReload} className="flex-1">
                  Sayfayı Yenile
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook version for functional components
export const useErrorHandler = () => {
  const handleError = (error: Error, errorInfo?: string) => {
    console.error("Error caught by useErrorHandler:", error, errorInfo);
    // You can also send error to logging service here
  };

  return { handleError };
};
