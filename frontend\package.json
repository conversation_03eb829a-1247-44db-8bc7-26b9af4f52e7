{"name": "@atropos/frontend", "private": true, "version": "1.0.0", "main": "dist-electron/main.js", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:electron": "tsc electron/main.ts --outDir dist-electron && tsc electron/preload.ts --outDir dist-electron", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && npm run build:electron && cross-env NODE_ENV=development electron dist-electron/main.js\"", "electron:build": "npm run build && npm run build:electron && electron-builder", "preview": "vite preview", "format": "prettier --write src/", "format:check": "prettier --check src/"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@tanstack/react-query": "^5.83.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.0", "react-router-dom": "^7.6.3", "tailwind-merge": "^3.3.1", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^37.2.1", "electron-builder": "^26.0.12", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.3.5", "typescript": "~5.8.3", "vite": "^7.0.4", "wait-on": "^8.0.3"}, "build": {"appId": "com.atropos.pos", "productName": "Atropos POS", "directories": {"output": "dist-app"}, "files": ["dist/**/*", "dist-electron/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}