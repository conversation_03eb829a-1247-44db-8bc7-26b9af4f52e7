import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import { z } from "zod";
import { prisma } from "../index";

// JWT payload tipi
interface JWTPayload {
  id: string;
  role: string;
  branchId: string | null;
  companyId: string;
}

// Fastify instance'ını auth plugin decorators ile genişlet
declare module "fastify" {
  interface FastifyInstance {
    authenticate: (
      request: FastifyRequest,
      reply: FastifyReply
    ) => Promise<void>;
    authorize: (
      roles: string[]
    ) => (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
  }
}

// Validation şemaları
const createProductVariantSchema = z.object({
  productId: z.string().cuid("Geçersiz ürün ID"),
  name: z
    .string()
    .min(1, "Varyant adı zorunludur")
    .max(100, "Varyant adı en fazla 100 karakter olabilir"),
  code: z
    .string()
    .min(1, "Varyant kodu zorunludur")
    .max(20, "Varyant kodu en fazla 20 karakter olabilir"),
  sku: z.string().max(50, "SKU en fazla 50 karakter olabilir").optional(),
  barcode: z
    .string()
    .max(50, "Barkod en fazla 50 karakter olabilir")
    .optional(),
  price: z.number().positive("Fiyat pozitif bir sayı olmalıdır"),
  costPrice: z.number().min(0, "Maliyet fiyatı negatif olamaz").optional(),
  displayOrder: z
    .number()
    .int()
    .min(0, "Görüntüleme sırası negatif olamaz")
    .default(0),
  active: z.boolean().default(true),
});

const updateProductVariantSchema = createProductVariantSchema.partial().omit({
  productId: true,
});

const querySchema = z.object({
  page: z
    .string()
    .transform(val => parseInt(val))
    .pipe(z.number().int().min(1))
    .default("1"),
  limit: z
    .string()
    .transform(val => parseInt(val))
    .pipe(z.number().int().min(1).max(100))
    .default("20"),
  search: z.string().optional(),
  productId: z.string().cuid().optional(),
  active: z
    .string()
    .transform(val => val === "true")
    .pipe(z.boolean())
    .optional(),
  sortBy: z
    .enum(["name", "price", "displayOrder", "createdAt"])
    .default("displayOrder"),
  sortOrder: z.enum(["asc", "desc"]).default("asc"),
});

export default async function (fastify: FastifyInstance) {
  // Ürün varyantlarını listele
  fastify.get(
    "/",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user as JWTPayload;
        const query = querySchema.parse(request.query);

        // Where koşulları
        const where: any = {
          deletedAt: null,
          product: {
            companyId: user.companyId,
            deletedAt: null,
          },
        };

        // Arama
        if (query.search) {
          where.OR = [
            { name: { contains: query.search, mode: "insensitive" } },
            { code: { contains: query.search, mode: "insensitive" } },
            { sku: { contains: query.search, mode: "insensitive" } },
            { barcode: { contains: query.search, mode: "insensitive" } },
          ];
        }

        // Filtreler
        if (query.productId) {
          where.productId = query.productId;
        }

        if (query.active !== undefined) {
          where.active = query.active;
        }

        // Sıralama
        const orderBy: any = {};
        orderBy[query.sortBy] = query.sortOrder;

        // Sayfalama hesaplamaları
        const skip = (query.page - 1) * query.limit;

        // Toplam kayıt sayısı
        const total = await prisma.productVariant.count({ where });

        // Varyantları getir
        const variants = await prisma.productVariant.findMany({
          where,
          include: {
            product: {
              select: {
                id: true,
                name: true,
                code: true,
                basePrice: true,
              },
            },
          },
          orderBy,
          skip,
          take: query.limit,
        });

        // Sayfalama bilgileri
        const totalPages = Math.ceil(total / query.limit);
        const hasNextPage = query.page < totalPages;
        const hasPrevPage = query.page > 1;

        return reply.send({
          success: true,
          data: {
            data: variants,
            pagination: {
              page: query.page,
              limit: query.limit,
              total,
              totalPages,
              hasNextPage,
              hasPrevPage,
            },
          },
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(400).send({
          success: false,
          error: "Bad Request",
          message:
            error instanceof z.ZodError
              ? "Geçersiz sorgu parametreleri"
              : "Varyantlar getirilirken bir hata oluştu",
        });
      }
    }
  );

  // Tek varyant detayı
  fastify.get(
    "/:id",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user as JWTPayload;
        const { id } = request.params as { id: string };

        const variant = await prisma.productVariant.findFirst({
          where: {
            id,
            deletedAt: null,
            product: {
              companyId: user.companyId,
              deletedAt: null,
            },
          },
          include: {
            product: {
              select: {
                id: true,
                name: true,
                code: true,
                basePrice: true,
                category: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        });

        if (!variant) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Varyant bulunamadı",
          });
        }

        return reply.send({
          success: true,
          data: variant,
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(500).send({
          success: false,
          error: "Internal Server Error",
          message: "Varyant getirilirken bir hata oluştu",
        });
      }
    }
  );

  // Yeni varyant oluştur
  fastify.post(
    "/",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user as JWTPayload;
        const variantData = createProductVariantSchema.parse(request.body);

        // Ürün kontrolü
        const product = await prisma.product.findFirst({
          where: {
            id: variantData.productId,
            companyId: user.companyId,
            deletedAt: null,
          },
        });

        if (!product) {
          return reply.status(400).send({
            success: false,
            error: "Bad Request",
            message: "Geçersiz ürün",
          });
        }

        // SKU benzersizlik kontrolü (aynı ürün içinde)
        if (variantData.sku) {
          const existingSku = await prisma.productVariant.findFirst({
            where: {
              productId: variantData.productId,
              sku: variantData.sku,
              deletedAt: null,
            },
          });

          if (existingSku) {
            return reply.status(400).send({
              success: false,
              error: "Bad Request",
              message: "Bu SKU zaten kullanılıyor",
            });
          }
        }

        // Barkod benzersizlik kontrolü (aynı ürün içinde)
        if (variantData.barcode) {
          const existingBarcode = await prisma.productVariant.findFirst({
            where: {
              productId: variantData.productId,
              barcode: variantData.barcode,
              deletedAt: null,
            },
          });

          if (existingBarcode) {
            return reply.status(400).send({
              success: false,
              error: "Bad Request",
              message: "Bu barkod zaten kullanılıyor",
            });
          }
        }

        // Varyant oluştur
        const variant = await prisma.productVariant.create({
          data: variantData,
          include: {
            product: {
              select: {
                id: true,
                name: true,
                code: true,
                basePrice: true,
              },
            },
          },
        });

        // Ürünün hasVariants flag'ini güncelle
        await prisma.product.update({
          where: { id: variantData.productId },
          data: { hasVariants: true },
        });

        return reply.status(201).send({
          success: true,
          data: variant,
          message: "Varyant başarıyla oluşturuldu",
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(400).send({
          success: false,
          error: "Bad Request",
          message:
            error instanceof z.ZodError
              ? "Geçersiz varyant bilgileri"
              : "Varyant oluşturulurken bir hata oluştu",
        });
      }
    }
  );

  // Varyant güncelle
  fastify.put(
    "/:id",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user as JWTPayload;
        const { id } = request.params as { id: string };
        const variantData = updateProductVariantSchema.parse(request.body);

        // Mevcut varyant kontrolü
        const existingVariant = await prisma.productVariant.findFirst({
          where: {
            id,
            deletedAt: null,
            product: {
              companyId: user.companyId,
              deletedAt: null,
            },
          },
        });

        if (!existingVariant) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Varyant bulunamadı",
          });
        }

        // SKU benzersizlik kontrolü (aynı ürün içinde, kendisi hariç)
        if (variantData.sku) {
          const existingSku = await prisma.productVariant.findFirst({
            where: {
              productId: existingVariant.productId,
              sku: variantData.sku,
              id: { not: id },
              deletedAt: null,
            },
          });

          if (existingSku) {
            return reply.status(400).send({
              success: false,
              error: "Bad Request",
              message: "Bu SKU zaten kullanılıyor",
            });
          }
        }

        // Barkod benzersizlik kontrolü (aynı ürün içinde, kendisi hariç)
        if (variantData.barcode) {
          const existingBarcode = await prisma.productVariant.findFirst({
            where: {
              productId: existingVariant.productId,
              barcode: variantData.barcode,
              id: { not: id },
              deletedAt: null,
            },
          });

          if (existingBarcode) {
            return reply.status(400).send({
              success: false,
              error: "Bad Request",
              message: "Bu barkod zaten kullanılıyor",
            });
          }
        }

        // Varyant güncelle
        const variant = await prisma.productVariant.update({
          where: { id },
          data: variantData,
          include: {
            product: {
              select: {
                id: true,
                name: true,
                code: true,
                basePrice: true,
              },
            },
          },
        });

        return reply.send({
          success: true,
          data: variant,
          message: "Varyant başarıyla güncellendi",
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(400).send({
          success: false,
          error: "Bad Request",
          message:
            error instanceof z.ZodError
              ? "Geçersiz varyant bilgileri"
              : "Varyant güncellenirken bir hata oluştu",
        });
      }
    }
  );

  // Varyant sil (soft delete)
  fastify.delete(
    "/:id",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user as JWTPayload;
        const { id } = request.params as { id: string };

        // Mevcut varyant kontrolü
        const existingVariant = await prisma.productVariant.findFirst({
          where: {
            id,
            deletedAt: null,
            product: {
              companyId: user.companyId,
              deletedAt: null,
            },
          },
        });

        if (!existingVariant) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Varyant bulunamadı",
          });
        }

        // Soft delete
        await prisma.productVariant.update({
          where: { id },
          data: { deletedAt: new Date() },
        });

        // Ürünün kalan varyant sayısını kontrol et
        const remainingVariants = await prisma.productVariant.count({
          where: {
            productId: existingVariant.productId,
            deletedAt: null,
          },
        });

        // Eğer hiç varyant kalmadıysa hasVariants flag'ini false yap
        if (remainingVariants === 0) {
          await prisma.product.update({
            where: { id: existingVariant.productId },
            data: { hasVariants: false },
          });
        }

        return reply.send({
          success: true,
          message: "Varyant başarıyla silindi",
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(500).send({
          success: false,
          error: "Internal Server Error",
          message: "Varyant silinirken bir hata oluştu",
        });
      }
    }
  );

  // Varyant durumunu değiştir (aktif/pasif)
  fastify.put(
    "/:id/toggle-status",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user as JWTPayload;
        const { id } = request.params as { id: string };

        // Mevcut varyant kontrolü
        const existingVariant = await prisma.productVariant.findFirst({
          where: {
            id,
            deletedAt: null,
            product: {
              companyId: user.companyId,
              deletedAt: null,
            },
          },
        });

        if (!existingVariant) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Varyant bulunamadı",
          });
        }

        // Durumu değiştir
        const variant = await prisma.productVariant.update({
          where: { id },
          data: { active: !existingVariant.active },
          include: {
            product: {
              select: {
                id: true,
                name: true,
                code: true,
                basePrice: true,
              },
            },
          },
        });

        return reply.send({
          success: true,
          data: variant,
          message: "Varyant durumu başarıyla güncellendi",
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(500).send({
          success: false,
          error: "Internal Server Error",
          message: "Varyant durumu güncellenirken bir hata oluştu",
        });
      }
    }
  );
}
