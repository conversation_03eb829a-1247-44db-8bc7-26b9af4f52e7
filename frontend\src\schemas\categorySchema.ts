import { z } from "zod";

const categoryFormSchemaDefinition = z.object({
  // Temel bilgiler
  name: z
    .string()
    .min(2, "Kategori adı en az 2 karakter olmalıdır")
    .max(100, "Kategori adı en fazla 100 karakter olabilir")
    .trim(),

  description: z.string().default(""),

  // Hiyerarşi
  parentId: z.string().default(""),

  // Görsel bilgiler
  color: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/, "Geçersiz hex renk formatı (#RRGGBB)")
    .or(z.literal(""))
    .default(""),

  icon: z.string().default(""),

  // Mutfak ayarları
  showInKitchen: z.boolean().default(true),

  preparationTime: z
    .number()
    .int("Hazırlık süresi tam sayı olmalıdır")
    .min(0, "Hazırlık süresi negatif olamaz")
    .default(0),

  // Görüntüleme ayarları
  displayOrder: z
    .number()
    .int("Görüntüleme sırası tam sayı olmalıdır")
    .min(0, "Görüntüleme sırası negatif olamaz")
    .default(0),

  showInMenu: z.boolean().default(true),

  // Durum
  active: z.boolean().default(true),
});

export const categoryFormSchema = categoryFormSchemaDefinition;
export type CategoryFormData = z.infer<typeof categoryFormSchema>;

// Güncelleme için kısmi şema
export const updateCategoryFormSchema = categoryFormSchema.partial();
export type UpdateCategoryFormData = z.infer<typeof updateCategoryFormSchema>;

// Form varsayılan değerleri
export const getDefaultCategoryFormValues = (): CategoryFormData => ({
  name: "",
  description: "",
  parentId: "",
  color: "",
  icon: "",
  showInKitchen: true,
  preparationTime: 0,
  displayOrder: 0,
  showInMenu: true,
  active: true,
});

// Kategori verilerini form verilerine dönüştürme
export const categoryToFormData = (category: any): CategoryFormData => ({
  name: category.name || "",
  description: category.description || "",
  parentId: category.parentId || "",
  color: category.color || "",
  icon: category.icon || "",
  showInKitchen:
    category.showInKitchen !== undefined ? category.showInKitchen : true,
  preparationTime: category.preparationTime || 0,
  displayOrder: category.displayOrder || 0,
  showInMenu: category.showInMenu !== undefined ? category.showInMenu : true,
  active: category.active !== undefined ? category.active : true,
});
